"""Main CLI interface for Tesla Inventory Monitor."""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm
from loguru import logger

from ..config.settings import Settings, get_settings, create_default_config
from ..core.monitor import InventoryMonitor
from ..notifications.notifier import NotificationManager
from ..utils.logging import setup_logging
from ..models.vehicle import VehicleStatus
from .. import __version__

app = typer.Typer(
    name="tesla-monitor",
    help="Tesla Inventory Monitor - Monitor Tesla Model Y inventory in Turkey",
    add_completion=False
)

console = Console()


def version_callback(value: bool):
    """Show version information."""
    if value:
        console.print(f"Tesla Inventory Monitor v{__version__}")
        raise typer.Exit()


@app.callback()
def main_callback(
    version: Optional[bool] = typer.Option(
        None, "--version", "-v", callback=version_callback, is_eager=True,
        help="Show version and exit"
    ),
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    )
):
    """Tesla Inventory Monitor - Monitor Tesla Model Y inventory in Turkey."""
    pass


@app.command()
def init(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Initialize configuration file."""
    if config_file is None:
        config_file = Path("config.json")
    
    if config_file.exists():
        if not Confirm.ask(f"Configuration file {config_file} already exists. Overwrite?"):
            console.print("❌ Initialization cancelled")
            raise typer.Exit(1)
    
    try:
        created_path = create_default_config(config_file)
        console.print(f"✅ Configuration file created: {created_path}")
        console.print("\n📝 Next steps:")
        console.print("1. Edit the configuration file to set your preferences")
        console.print("2. Run 'tesla-monitor test' to verify connection")
        console.print("3. Run 'tesla-monitor start' to begin monitoring")
    except Exception as e:
        console.print(f"❌ Failed to create configuration: {e}")
        raise typer.Exit(1)


@app.command()
def start(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    once: bool = typer.Option(False, "--once", help="Run once and exit")
):
    """Start monitoring Tesla inventory."""
    try:
        settings = get_settings(config_file)
        if once:
            settings.monitoring.run_once = True
        
        setup_logging(settings)
        
        console.print(Panel.fit(
            "🚗 Tesla Inventory Monitor Starting",
            style="bold green"
        ))
        
        asyncio.run(_start_monitoring(settings))
        
    except KeyboardInterrupt:
        console.print("\n⏹️  Monitoring stopped by user")
    except Exception as e:
        console.print(f"❌ Error starting monitor: {e}")
        raise typer.Exit(1)


@app.command()
def check(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Check inventory once and display results."""
    try:
        settings = get_settings(config_file)
        settings.monitoring.run_once = True
        setup_logging(settings)
        
        asyncio.run(_check_inventory(settings))
        
    except Exception as e:
        console.print(f"❌ Error checking inventory: {e}")
        raise typer.Exit(1)


@app.command()
def test(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Test Tesla API connection and notifications."""
    try:
        settings = get_settings(config_file)
        setup_logging(settings)
        
        asyncio.run(_test_connection(settings))
        
    except Exception as e:
        console.print(f"❌ Test failed: {e}")
        raise typer.Exit(1)


@app.command("config")
def config_command():
    """Configuration management commands."""
    pass


@config_command.command("show")
def config_show(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Show current configuration."""
    try:
        settings = get_settings(config_file)
        
        console.print(Panel.fit("📋 Current Configuration", style="bold blue"))
        
        # API Configuration
        table = Table(title="API Settings")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Base URL", settings.api.base_url)
        table.add_row("Timeout", f"{settings.api.timeout_seconds}s")
        table.add_row("Max Retries", str(settings.api.max_retries))
        table.add_row("Rate Limit", f"{settings.api.rate_limit_delay}s")
        
        console.print(table)
        
        # Filter Configuration
        table = Table(title="Filter Settings")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Model", settings.filters.model.upper())
        table.add_row("Market", settings.filters.market)
        table.add_row("Condition", ", ".join(settings.filters.condition))
        table.add_row("Price Range", f"{settings.filters.price_min or 'No min'} - {settings.filters.price_max or 'No max'}")
        table.add_row("Year Range", f"{settings.filters.year_min or 'No min'} - {settings.filters.year_max or 'No max'}")
        
        console.print(table)
        
        # Monitoring Configuration
        table = Table(title="Monitoring Settings")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Check Interval", f"{settings.monitoring.check_interval_minutes} minutes")
        table.add_row("Notifications", "✅ Enabled" if settings.monitoring.enable_notifications else "❌ Disabled")
        table.add_row("Desktop Notifications", "✅ Enabled" if settings.notifications.desktop.enabled else "❌ Disabled")
        table.add_row("Email Notifications", "✅ Enabled" if settings.notifications.email.enabled else "❌ Disabled")
        
        console.print(table)
        
    except Exception as e:
        console.print(f"❌ Error showing configuration: {e}")
        raise typer.Exit(1)


@config_command.command("edit")
def config_edit(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Edit configuration file."""
    if config_file is None:
        config_file = Path("config.json")
    
    if not config_file.exists():
        console.print(f"❌ Configuration file {config_file} not found")
        console.print("Run 'tesla-monitor init' to create it")
        raise typer.Exit(1)
    
    import os
    try:
        if sys.platform == "win32":
            os.startfile(config_file)
        else:
            os.system(f"open {config_file}")
        console.print(f"📝 Opening {config_file} in default editor")
    except Exception as e:
        console.print(f"❌ Failed to open editor: {e}")
        console.print(f"Please manually edit: {config_file}")


@app.command("db")
def db_command():
    """Database management commands."""
    pass


@db_command.command("status")
def db_status(
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Show database statistics."""
    try:
        settings = get_settings(config_file)
        setup_logging(settings)
        
        asyncio.run(_show_db_status(settings))
        
    except Exception as e:
        console.print(f"❌ Error showing database status: {e}")
        raise typer.Exit(1)


@db_command.command("export")
def db_export(
    output: Path = typer.Argument(..., help="Output CSV file path"),
    config_file: Optional[Path] = typer.Option(None, "--config", "-c", help="Configuration file path")
):
    """Export vehicle data to CSV."""
    try:
        settings = get_settings(config_file)
        setup_logging(settings)
        
        asyncio.run(_export_vehicles(settings, output))
        
    except Exception as e:
        console.print(f"❌ Error exporting data: {e}")
        raise typer.Exit(1)


async def _start_monitoring(settings: Settings):
    """Start the monitoring process."""
    monitor = InventoryMonitor(settings)
    notifier = NotificationManager(settings)
    
    await monitor.initialize()
    
    try:
        # Start monitoring in background
        monitor_task = asyncio.create_task(monitor.start_monitoring())
        
        # Notification loop
        while monitor.is_running:
            try:
                # Check for new vehicles to notify
                new_vehicles = await monitor.get_new_vehicles()
                if new_vehicles:
                    await notifier.notify_new_vehicles(new_vehicles)
                    await monitor.mark_vehicles_notified(new_vehicles)
                
                # Wait before next notification check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in notification loop: {e}")
                await asyncio.sleep(60)
        
        # Wait for monitor to finish
        await monitor_task
        
    except KeyboardInterrupt:
        console.print("\n⏹️  Stopping monitor...")
        monitor.stop_monitoring()


async def _check_inventory(settings: Settings):
    """Check inventory once and display results."""
    monitor = InventoryMonitor(settings)
    
    await monitor.initialize()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Checking Tesla inventory...", total=None)
        
        try:
            vehicles = await monitor.check_inventory()
            progress.remove_task(task)
            
            if vehicles:
                console.print(f"\n🎉 Found {len(vehicles)} new vehicles!")
                
                table = Table(title="New Vehicles Found")
                table.add_column("Vehicle", style="cyan")
                table.add_column("Price", style="green")
                table.add_column("Year", style="blue")
                table.add_column("VIN", style="dim")
                
                for vehicle in vehicles:
                    table.add_row(
                        vehicle.display_name,
                        vehicle.formatted_price,
                        str(vehicle.year),
                        vehicle.vin
                    )
                
                console.print(table)
            else:
                console.print("ℹ️  No new vehicles found matching your criteria")
                
        except Exception as e:
            progress.remove_task(task)
            raise e


async def _test_connection(settings: Settings):
    """Test API connection and notifications."""
    console.print(Panel.fit("🧪 Testing Tesla Monitor", style="bold yellow"))
    
    # Test API connection
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Testing Tesla API connection...", total=None)
        
        monitor = InventoryMonitor(settings)
        await monitor.initialize()
        
        success = await monitor.test_connection()
        progress.remove_task(task)
        
        if success:
            console.print("✅ Tesla API connection successful")
        else:
            console.print("❌ Tesla API connection failed")
            return
    
    # Test notifications
    notifier = NotificationManager(settings)
    
    if settings.notifications.desktop.enabled:
        console.print("🔔 Testing desktop notifications...")
        success = await notifier.test_desktop_notification()
        if success:
            console.print("✅ Desktop notifications working")
        else:
            console.print("❌ Desktop notifications failed")
    
    if settings.notifications.email.enabled:
        console.print("📧 Testing email notifications...")
        success = await notifier.test_email_notification()
        if success:
            console.print("✅ Email notifications working")
        else:
            console.print("❌ Email notifications failed")
    
    console.print("\n🎉 Testing complete!")


async def _show_db_status(settings: Settings):
    """Show database status."""
    monitor = InventoryMonitor(settings)
    await monitor.initialize()
    
    stats = await monitor.get_statistics()
    
    console.print(Panel.fit("📊 Database Statistics", style="bold blue"))
    
    table = Table()
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Total Vehicles", str(stats.get('total_vehicles', 0)))
    table.add_row("Seen Last 24h", str(stats.get('seen_last_24h', 0)))
    
    # Status breakdown
    status_counts = stats.get('by_status', {})
    for status, count in status_counts.items():
        table.add_row(f"Status: {status.title()}", str(count))
    
    # Price statistics
    price_stats = stats.get('price_stats', {})
    if price_stats:
        table.add_row("Min Price", f"{price_stats['min']:,} TRY")
        table.add_row("Max Price", f"{price_stats['max']:,} TRY")
        table.add_row("Avg Price", f"{price_stats['avg']:,} TRY")
    
    console.print(table)


async def _export_vehicles(settings: Settings, output_path: Path):
    """Export vehicles to CSV."""
    monitor = InventoryMonitor(settings)
    await monitor.initialize()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Exporting vehicle data...", total=None)
        
        count = await monitor.export_vehicles(str(output_path))
        progress.remove_task(task)
        
        console.print(f"✅ Exported {count} vehicles to {output_path}")


def main():
    """Main entry point."""
    app()


if __name__ == "__main__":
    main()
