{"api": {"base_url": "https://www.tesla.com", "inventory_endpoint": "/inventory/api/v1/inventory-results", "timeout_seconds": 30, "max_retries": 3, "retry_delay_seconds": 2, "rate_limit_delay": 1.0, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, "filters": {"model": "my", "market": "TR", "condition": ["new", "used"], "price_min": null, "price_max": 2000000, "year_min": 2022, "year_max": null, "trim_levels": [], "colors": []}, "monitoring": {"check_interval_minutes": 5, "enable_notifications": true, "notification_sound": true, "max_concurrent_requests": 3, "run_once": false}, "notifications": {"desktop": {"enabled": true, "title": "Tesla Monitor", "timeout": 10}, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "to_email": "", "use_tls": true}}, "database": {"path": "vehicles.db", "cleanup_days": 30}, "logging": {"level": "INFO", "file": "tesla_monitor.log", "max_size_mb": 10, "backup_count": 5, "console_output": true}}