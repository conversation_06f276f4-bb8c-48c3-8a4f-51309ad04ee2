"""Tesla API client for inventory monitoring."""

import asyncio
import json
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlencode
import httpx
from loguru import logger

from config.settings import TeslaMonitorConfig


class TeslaApiError(Exception):
    """Custom exception for Tesla API errors."""
    pass


class TeslaApiClient:
    """Async Tesla API client for inventory operations."""
    
    def __init__(self, config: TeslaMonitorConfig):
        self.config = config
        self.base_url = config.api.base_url
        self.session: Optional[httpx.AsyncClient] = None
        self._rate_limit_delay = config.api.rate_limit_delay
        
        # Headers to mimic a real browser more accurately
        self.headers = {
            'User-Agent': config.monitoring.user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.tesla.com/',
            'Origin': 'https://www.tesla.com',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()
    
    async def start_session(self):
        """Initialize the HTTP session."""
        if self.session is None:
            timeout = httpx.Timeout(self.config.monitoring.timeout_seconds)
            self.session = httpx.AsyncClient(
                headers=self.headers,
                timeout=timeout,
                follow_redirects=True
            )
            logger.debug("Tesla API client session started")
    
    async def close_session(self):
        """Close the HTTP session."""
        if self.session:
            await self.session.aclose()
            self.session = None
            logger.debug("Tesla API client session closed")
    
    async def _make_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """Make HTTP request with retry logic."""
        if not self.session:
            await self.start_session()

        last_exception = None
        last_error_details = "Unknown error"

        for attempt in range(self.config.monitoring.max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                logger.debug(f"Request headers: {dict(self.session.headers)}")
                logger.debug(f"Request kwargs: {kwargs}")

                response = await self.session.request(method, url, **kwargs)

                logger.debug(f"Response status: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")

                # Rate limiting
                if self._rate_limit_delay > 0:
                    await asyncio.sleep(self._rate_limit_delay)

                return response

            except httpx.TimeoutException as e:
                last_exception = e
                last_error_details = f"Timeout after {self.config.monitoring.timeout_seconds}s: {str(e)}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.monitoring.max_retries}): {last_error_details}")

            except httpx.ConnectError as e:
                last_exception = e
                last_error_details = f"Connection error: {str(e)}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.monitoring.max_retries}): {last_error_details}")

            except httpx.HTTPStatusError as e:
                last_exception = e
                last_error_details = f"HTTP {e.response.status_code}: {e.response.text[:200]}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.monitoring.max_retries}): {last_error_details}")

            except httpx.RequestError as e:
                last_exception = e
                last_error_details = f"Request error: {str(e)} (Type: {type(e).__name__})"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.monitoring.max_retries}): {last_error_details}")

            except Exception as e:
                last_exception = e
                last_error_details = f"Unexpected error: {str(e)} (Type: {type(e).__name__})"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.monitoring.max_retries}): {last_error_details}")

            # Retry logic
            if attempt < self.config.monitoring.max_retries - 1:
                delay = self.config.monitoring.retry_delay_seconds * (2 ** attempt)
                logger.info(f"Retrying in {delay} seconds...")
                await asyncio.sleep(delay)

        # Final error with detailed information
        error_msg = f"Request failed after {self.config.monitoring.max_retries} attempts. Last error: {last_error_details}"
        logger.error(f"All retry attempts exhausted. {error_msg}")
        raise TeslaApiError(error_msg)
    
    def _build_inventory_params(self) -> Dict[str, Any]:
        """Build query parameters for inventory API."""
        params = {
            'query': json.dumps({
                'model': self.config.filters.model,
                'condition': self.config.filters.condition[0] if self.config.filters.condition else 'new',
                'options': {},
                'arrangeby': 'Price',
                'order': 'asc',
                'market': self.config.filters.market,
                'language': 'en',
                'super_region': 'north_america',
                'lng': 28.9784,  # Istanbul coordinates
                'lat': 41.0082,
                'zip': '34000',
                'range': 0
            }),
            'offset': 0,
            'count': 50,
            'outsideOffset': 0,
            'outsideSearch': False
        }
        
        return params
    
    async def get_inventory(self) -> List[Dict[str, Any]]:
        """Fetch Tesla inventory data."""
        try:
            # Build the full URL
            endpoint = self.config.api.inventory_endpoint
            url = urljoin(self.base_url, endpoint)
            
            # Build query parameters
            params = self._build_inventory_params()
            
            logger.debug(f"Fetching inventory from {url}")
            logger.debug(f"Query parameters: {params}")
            
            # Make the request
            response = await self._make_request('GET', url, params=params)
            
            # Check response status
            if response.status_code == 200:
                data = response.json()
                vehicles = data.get('results', [])
                logger.info(f"Successfully fetched {len(vehicles)} vehicles from Tesla API")
                return vehicles
            
            elif response.status_code == 429:
                logger.warning("Rate limited by Tesla API")
                raise TeslaApiError("Rate limited by Tesla API")
            
            else:
                logger.error(f"Tesla API returned status {response.status_code}: {response.text}")
                raise TeslaApiError(f"API request failed with status {response.status_code}")
        
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Tesla API response: {e}")
            raise TeslaApiError(f"Invalid JSON response: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error fetching inventory: {e}")
            raise TeslaApiError(f"Unexpected error: {e}")
    
    async def test_connection(self) -> bool:
        """Test connection to Tesla API."""
        try:
            # Try to fetch a small amount of data
            vehicles = await self.get_inventory()
            logger.info("Tesla API connection test successful")
            return True
        except Exception as e:
            logger.error(f"Tesla API connection test failed: {e}")
            return False
    
    def filter_vehicles(self, vehicles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply local filters to vehicle list."""
        filtered = []
        
        for vehicle in vehicles:
            # Extract vehicle data
            price = vehicle.get('Price')
            year = vehicle.get('Year')
            condition = vehicle.get('condition', '').lower()
            trim = vehicle.get('TrimName', '')
            color = vehicle.get('PAINT', [])
            
            # Apply price filter
            if self.config.filters.price_min and price and price < self.config.filters.price_min:
                continue
            if self.config.filters.price_max and price and price > self.config.filters.price_max:
                continue
            
            # Apply year filter
            if self.config.filters.year_min and year and year < self.config.filters.year_min:
                continue
            if self.config.filters.year_max and year and year > self.config.filters.year_max:
                continue
            
            # Apply condition filter
            if self.config.filters.condition and condition not in [c.lower() for c in self.config.filters.condition]:
                continue
            
            # Apply trim filter
            if self.config.filters.trim_levels and trim not in self.config.filters.trim_levels:
                continue
            
            # Apply color filter (if specified)
            if self.config.filters.colors:
                vehicle_colors = [c.get('value', '') for c in color] if isinstance(color, list) else []
                if not any(c in self.config.filters.colors for c in vehicle_colors):
                    continue
            
            filtered.append(vehicle)
        
        logger.info(f"Filtered {len(vehicles)} vehicles down to {len(filtered)} matching criteria")
        return filtered