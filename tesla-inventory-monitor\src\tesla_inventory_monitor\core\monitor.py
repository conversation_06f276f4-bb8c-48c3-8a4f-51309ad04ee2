"""Tesla inventory monitoring logic."""

import asyncio
from datetime import datetime
from typing import List, Set, Optional
from loguru import logger

from ..config.settings import Settings
from ..models.vehicle import Vehicle, VehicleStatus
from ..models.database import DatabaseManager
from .client import TeslaInventoryClient, TeslaApiError


class InventoryMonitor:
    """Tesla inventory monitoring service."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = TeslaInventoryClient(settings)
        self.db = DatabaseManager(settings.database.full_path)
        self._running = False
        self._stop_event = asyncio.Event()
    
    async def initialize(self) -> None:
        """Initialize the monitor."""
        await self.db.initialize()
        logger.info("Inventory monitor initialized")
    
    async def start_monitoring(self) -> None:
        """Start continuous monitoring."""
        if self._running:
            logger.warning("Monitor is already running")
            return
        
        self._running = True
        self._stop_event.clear()
        
        logger.info("Starting Tesla inventory monitoring...")
        logger.info(f"Check interval: {self.settings.monitoring.check_interval_minutes} minutes")
        logger.info(f"Filters: Model {self.settings.filters.model.upper()}, "
                   f"Market {self.settings.filters.market}, "
                   f"Price {self.settings.filters.price_min}-{self.settings.filters.price_max}")
        
        try:
            async with self.client:
                while self._running and not self._stop_event.is_set():
                    try:
                        await self.check_inventory()
                        
                        if self.settings.monitoring.run_once:
                            logger.info("Single check completed, stopping monitor")
                            break
                        
                        # Wait for next check or stop signal
                        wait_seconds = self.settings.monitoring.check_interval_minutes * 60
                        try:
                            await asyncio.wait_for(
                                self._stop_event.wait(), 
                                timeout=wait_seconds
                            )
                            # If we get here, stop was requested
                            break
                        except asyncio.TimeoutError:
                            # Timeout is expected, continue monitoring
                            continue
                    
                    except TeslaApiError as e:
                        logger.error(f"Tesla API error during monitoring: {e}")
                        # Wait a bit before retrying
                        await asyncio.sleep(60)
                    
                    except Exception as e:
                        logger.error(f"Unexpected error during monitoring: {e}")
                        # Wait a bit before retrying
                        await asyncio.sleep(60)
        
        finally:
            self._running = False
            logger.info("Tesla inventory monitoring stopped")
    
    def stop_monitoring(self) -> None:
        """Stop monitoring."""
        if self._running:
            logger.info("Stopping Tesla inventory monitoring...")
            self._running = False
            self._stop_event.set()
    
    async def check_inventory(self) -> List[Vehicle]:
        """Check inventory once and process results."""
        logger.info("Checking Tesla inventory...")
        
        try:
            # Fetch current inventory
            current_vehicles = await self.client.get_inventory_with_filters()
            logger.info(f"Found {len(current_vehicles)} vehicles matching filters")
            
            if not current_vehicles:
                logger.info("No vehicles found matching criteria")
                return []
            
            # Process vehicles
            new_vehicles = await self._process_vehicles(current_vehicles)
            
            # Update sold vehicles
            current_vins = [v.vin for v in current_vehicles]
            sold_count = await self.db.mark_vehicles_as_sold(current_vins)
            
            if sold_count > 0:
                logger.info(f"Marked {sold_count} vehicles as sold")
            
            # Clean up old vehicles if configured
            if self.settings.database.cleanup_days > 0:
                cleaned_count = await self.db.cleanup_old_vehicles(self.settings.database.cleanup_days)
                if cleaned_count > 0:
                    logger.info(f"Cleaned up {cleaned_count} old vehicles")
            
            # Log summary
            if new_vehicles:
                logger.info(f"Found {len(new_vehicles)} new vehicles!")
                for vehicle in new_vehicles:
                    logger.info(f"  - {vehicle.display_name} ({vehicle.formatted_price}) - VIN: {vehicle.vin}")
            else:
                logger.info("No new vehicles found")
            
            return new_vehicles
        
        except Exception as e:
            logger.error(f"Error checking inventory: {e}")
            raise
    
    async def _process_vehicles(self, vehicles: List[Vehicle]) -> List[Vehicle]:
        """Process vehicles and identify new ones."""
        new_vehicles = []
        
        for vehicle in vehicles:
            # Check if vehicle exists in database
            existing_vehicle = await self.db.get_vehicle(vehicle.vin)
            
            if existing_vehicle:
                # Update existing vehicle
                existing_vehicle.update_last_seen()
                existing_vehicle.price = vehicle.price  # Update price in case it changed
                existing_vehicle.location = vehicle.location  # Update location
                existing_vehicle.delivery_estimate = vehicle.delivery_estimate  # Update delivery
                existing_vehicle.raw_data = vehicle.raw_data  # Update raw data
                
                await self.db.save_vehicle(existing_vehicle)
                logger.debug(f"Updated existing vehicle {vehicle.vin}")
            else:
                # New vehicle
                vehicle.status = VehicleStatus.NEW
                await self.db.save_vehicle(vehicle)
                new_vehicles.append(vehicle)
                logger.info(f"New vehicle found: {vehicle.display_name} - {vehicle.formatted_price}")
        
        return new_vehicles
    
    async def get_statistics(self) -> dict:
        """Get monitoring statistics."""
        stats = await self.db.get_statistics()
        stats['monitoring_status'] = 'running' if self._running else 'stopped'
        stats['last_check'] = datetime.now().isoformat()
        return stats
    
    async def get_new_vehicles(self) -> List[Vehicle]:
        """Get all new vehicles that haven't been notified."""
        return await self.db.get_new_vehicles()
    
    async def mark_vehicles_notified(self, vehicles: List[Vehicle]) -> None:
        """Mark vehicles as notified."""
        for vehicle in vehicles:
            vehicle.mark_notification_sent()
            await self.db.save_vehicle(vehicle)
        
        logger.info(f"Marked {len(vehicles)} vehicles as notified")
    
    async def get_all_vehicles(self, limit: Optional[int] = None) -> List[Vehicle]:
        """Get all vehicles from database."""
        return await self.db.get_all_vehicles(limit)
    
    async def get_vehicles_by_status(self, status: VehicleStatus) -> List[Vehicle]:
        """Get vehicles by status."""
        return await self.db.get_vehicles_by_status(status)
    
    async def export_vehicles(self, output_path: str) -> int:
        """Export vehicles to CSV."""
        from pathlib import Path
        return await self.db.export_to_csv(Path(output_path))
    
    async def test_connection(self) -> bool:
        """Test Tesla API connection."""
        async with self.client:
            return await self.client.test_connection()
    
    @property
    def is_running(self) -> bool:
        """Check if monitor is running."""
        return self._running
