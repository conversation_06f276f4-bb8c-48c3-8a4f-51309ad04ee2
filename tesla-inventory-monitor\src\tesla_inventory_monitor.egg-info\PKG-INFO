Metadata-Version: 2.4
Name: tesla-inventory-monitor
Version: 1.0.0
Summary: Modern Tesla inventory monitoring application for Turkey market
Author-email: Tesla Monitor <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/example/tesla-inventory-monitor
Project-URL: Repository, https://github.com/example/tesla-inventory-monitor
Project-URL: Issues, https://github.com/example/tesla-inventory-monitor/issues
Keywords: tesla,inventory,monitoring,turkey
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: End Users/Desktop
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: fastapi>=0.104.1
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: httpx>=0.25.2
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: rich>=13.7.0
Requires-Dist: typer>=0.9.0
Requires-Dist: click>=8.1.7
Requires-Dist: sqlalchemy>=2.0.23
Requires-Dist: aiosqlite>=0.19.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: loguru>=0.7.2
Requires-Dist: schedule>=1.2.0
Requires-Dist: plyer>=2.1.0
Requires-Dist: pywin32>=306; sys_platform == "win32"
Provides-Extra: dev
Requires-Dist: pytest>=7.4.3; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.1; extra == "dev"
Requires-Dist: pytest-mock>=3.12.0; extra == "dev"
Requires-Dist: black>=23.11.0; extra == "dev"
Requires-Dist: flake8>=6.1.0; extra == "dev"
Requires-Dist: mypy>=1.7.1; extra == "dev"
Dynamic: license-file

# Tesla Inventory Monitor

A modern, robust Tesla inventory monitoring application specifically designed for the Turkey market. Built from scratch with modern Python technologies, this application monitors Tesla Model Y inventory and sends notifications when new vehicles matching your criteria are found.

## Features

- 🚗 **Direct Tesla API Integration** - No third-party dependencies, direct HTTP requests to Tesla's public API
- 🇹🇷 **Turkey Market Focus** - Optimized for Turkish Tesla inventory
- 🔍 **Advanced Filtering** - Filter by price, year, condition, trim, and color
- 🔔 **Smart Notifications** - Desktop notifications and optional email alerts
- 💾 **Vehicle Tracking** - SQLite database to track previously seen vehicles
- 🎨 **Beautiful CLI** - Rich terminal interface with progress bars and formatting
- 🔄 **Continuous Monitoring** - Configurable check intervals with robust error handling
- 🪟 **Windows Optimized** - Built specifically for Windows 10/11
- ⚡ **Async Performance** - Modern async/await patterns for optimal performance
- 🛡️ **Robust Error Handling** - Network resilience with retry logic and rate limiting

## Technology Stack

- **Python 3.9+** - Modern Python with type hints
- **FastAPI** - Modern async web framework
- **httpx** - Async HTTP client for API requests
- **Rich** - Beautiful terminal formatting
- **Pydantic** - Data validation and settings management
- **SQLAlchemy** - Database ORM with async support
- **SQLite** - Lightweight database for vehicle tracking
- **Typer** - Modern CLI framework

## Quick Start

### Prerequisites

- Windows 10/11
- Python 3.9 or higher
- Internet connection

### Installation

1. **Clone or download the project:**
   ```cmd
   git clone <repository-url>
   cd tesla-inventory-monitor
   ```

2. **Create virtual environment:**
   ```cmd
   python -m venv venv
   venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```cmd
   pip install -r requirements.txt
   ```

4. **Install the application:**
   ```cmd
   pip install -e .
   ```

### Configuration

1. **Create configuration file:**
   ```cmd
   tesla-monitor init
   ```

2. **Edit configuration:**
   ```cmd
   tesla-monitor config edit
   ```

3. **Test connection:**
   ```cmd
   tesla-monitor test
   ```

### Usage

**Start monitoring:**
```cmd
tesla-monitor start
```

**Check current inventory:**
```cmd
tesla-monitor check
```

**View configuration:**
```cmd
tesla-monitor config show
```

**View help:**
```cmd
tesla-monitor --help
```

## Configuration

The application uses a JSON configuration file (`config.json`) with the following structure:

```json
{
  "api": {
    "base_url": "https://www.tesla.com",
    "inventory_endpoint": "/inventory/api/v1/inventory-results",
    "timeout_seconds": 30,
    "max_retries": 3,
    "retry_delay_seconds": 2,
    "rate_limit_delay": 1.0
  },
  "filters": {
    "model": "my",
    "market": "TR",
    "condition": ["new", "used"],
    "price_min": null,
    "price_max": 2000000,
    "year_min": 2022,
    "year_max": null,
    "trim_levels": [],
    "colors": []
  },
  "monitoring": {
    "check_interval_minutes": 5,
    "enable_notifications": true,
    "notification_sound": true,
    "max_concurrent_requests": 3
  },
  "notifications": {
    "desktop": {
      "enabled": true,
      "title": "Tesla Monitor",
      "timeout": 10
    },
    "email": {
      "enabled": false,
      "smtp_server": "smtp.gmail.com",
      "smtp_port": 587,
      "username": "",
      "password": "",
      "to_email": ""
    }
  },
  "database": {
    "path": "vehicles.db",
    "cleanup_days": 30
  },
  "logging": {
    "level": "INFO",
    "file": "tesla_monitor.log",
    "max_size_mb": 10,
    "backup_count": 5
  }
}
```

## Commands

### Monitoring Commands
- `tesla-monitor start` - Start continuous monitoring
- `tesla-monitor check` - Check inventory once
- `tesla-monitor stop` - Stop monitoring service

### Configuration Commands
- `tesla-monitor config show` - Display current configuration
- `tesla-monitor config edit` - Edit configuration file
- `tesla-monitor config reset` - Reset to default configuration

### Database Commands
- `tesla-monitor db status` - Show database statistics
- `tesla-monitor db cleanup` - Clean old vehicle records
- `tesla-monitor db export` - Export vehicle data to CSV

### Utility Commands
- `tesla-monitor test` - Test Tesla API connection
- `tesla-monitor init` - Initialize configuration
- `tesla-monitor version` - Show version information

## Development

### Setup Development Environment

```cmd
# Clone repository
git clone <repository-url>
cd tesla-inventory-monitor

# Create virtual environment
python -m venv venv
venv\Scripts\activate

# Install with development dependencies
pip install -e ".[dev]"
```

### Running Tests

```cmd
# Run all tests
pytest

# Run with coverage
pytest --cov=tesla_inventory_monitor

# Run specific test file
pytest tests/test_client.py
```

### Code Formatting

```cmd
# Format code
black src/ tests/

# Check formatting
black --check src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check internet connection
   - Verify Tesla API is accessible
   - Check firewall settings

2. **Rate Limiting**
   - Increase `rate_limit_delay` in configuration
   - Reduce `check_interval_minutes`

3. **No Notifications**
   - Check Windows notification settings
   - Verify notification permissions
   - Test with `tesla-monitor test-notification`

### Logs

Check the log file for detailed error information:
```cmd
type tesla_monitor.log
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions:
- Check the troubleshooting section
- Review the logs
- Create an issue on GitHub
