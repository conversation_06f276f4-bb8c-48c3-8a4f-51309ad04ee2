# Installation Guide - Tesla Inventory Monitor

This guide provides detailed installation instructions for Tesla Inventory Monitor on Windows 10/11.

## Prerequisites

### 1. Python 3.9 or Higher

Download and install Python from [python.org](https://www.python.org/downloads/):

1. Download the latest Python 3.9+ installer for Windows
2. **IMPORTANT**: Check "Add Python to PATH" during installation
3. Choose "Install for all users" if you have admin rights
4. Verify installation by opening Command Prompt and running:
   ```cmd
   python --version
   ```

### 2. Internet Connection

The application requires internet access to:
- Download dependencies during installation
- Connect to Tesla's API for inventory data
- Send email notifications (if configured)

## Installation Methods

### Method 1: Automated Installation (Recommended)

1. **Download the project** to your desired location
2. **Open Command Prompt as Administrator** (recommended)
3. **Navigate to the project directory**:
   ```cmd
   cd "C:\path\to\tesla-inventory-monitor"
   ```
4. **Run the installation script**:
   ```cmd
   install.bat
   ```

The script will:
- Check Python installation and version
- Create a virtual environment
- Install all dependencies
- Install the Tesla Monitor application
- Display next steps

### Method 2: Manual Installation

If the automated script doesn't work, follow these manual steps:

1. **Open Command Prompt** in the project directory
2. **Create virtual environment**:
   ```cmd
   python -m venv venv
   ```
3. **Activate virtual environment**:
   ```cmd
   venv\Scripts\activate.bat
   ```
4. **Upgrade pip**:
   ```cmd
   python -m pip install --upgrade pip
   ```
5. **Install dependencies**:
   ```cmd
   pip install -r requirements.txt
   ```
6. **Install the application**:
   ```cmd
   pip install -e .
   ```

## Post-Installation Setup

### 1. Initialize Configuration

```cmd
# Activate virtual environment (if not already active)
venv\Scripts\activate.bat

# Create default configuration
tesla-monitor init
```

This creates a `config.json` file with default settings.

### 2. Configure Settings

Edit the `config.json` file to customize:

- **Price filters**: Set minimum and maximum price limits
- **Year filters**: Set year range for vehicles
- **Notification settings**: Configure desktop and email notifications
- **Monitoring interval**: Set how often to check for new vehicles

Example configuration:
```json
{
  "filters": {
    "price_min": 1500000,
    "price_max": 2500000,
    "year_min": 2022
  },
  "monitoring": {
    "check_interval_minutes": 10
  },
  "notifications": {
    "desktop": {
      "enabled": true
    },
    "email": {
      "enabled": false
    }
  }
}
```

### 3. Test Connection

```cmd
tesla-monitor test
```

This will:
- Test connection to Tesla's API
- Verify notification settings
- Display any configuration issues

### 4. Start Monitoring

```cmd
tesla-monitor start
```

Or use the quick-run script:
```cmd
run.bat
```

## Troubleshooting

### Common Issues

#### 1. "Python is not recognized"
- Python is not installed or not in PATH
- Reinstall Python and check "Add Python to PATH"
- Restart Command Prompt after installation

#### 2. "Permission denied" errors
- Run Command Prompt as Administrator
- Check antivirus software isn't blocking the installation

#### 3. "Failed to install dependencies"
- Check internet connection
- Try updating pip: `python -m pip install --upgrade pip`
- Try installing dependencies one by one

#### 4. "Tesla API connection failed"
- Check internet connection
- Verify firewall isn't blocking the application
- Try running with different network (mobile hotspot)

#### 5. "Desktop notifications not working"
- Check Windows notification settings
- Ensure notifications are enabled for the application
- Try running as Administrator

### Getting Help

1. **Check the logs**: Look at `tesla_monitor.log` for detailed error information
2. **Run with debug logging**: Edit `config.json` and set `"logging": {"level": "DEBUG"}`
3. **Test individual components**:
   ```cmd
   tesla-monitor test
   tesla-monitor config show
   tesla-monitor db status
   ```

## Uninstallation

To remove Tesla Inventory Monitor:

1. **Stop any running monitors**
2. **Delete the project directory**
3. **Remove any scheduled tasks** (if you set them up)

The application doesn't modify system files outside its directory.

## Advanced Setup

### Running as a Windows Service

For advanced users who want Tesla Monitor to run automatically:

1. Install `nssm` (Non-Sucking Service Manager)
2. Create a service that runs `run.bat`
3. Configure the service to start automatically

### Scheduled Tasks

Use Windows Task Scheduler to run Tesla Monitor:

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (e.g., "At startup")
4. Set action to run `run.bat`

### Multiple Configurations

You can run multiple instances with different configurations:

```cmd
tesla-monitor --config config-model-y.json start
tesla-monitor --config config-model-3.json start
```

## Performance Tips

1. **Adjust check interval**: Longer intervals reduce API calls
2. **Use specific filters**: Narrow filters reduce processing time
3. **Regular cleanup**: Use `tesla-monitor db cleanup` periodically
4. **Monitor logs**: Check for errors or warnings regularly

## Security Considerations

1. **Email passwords**: Store email passwords securely
2. **Configuration files**: Protect config files with sensitive data
3. **Network access**: Monitor network traffic if on corporate networks
4. **Updates**: Keep dependencies updated for security patches
