#!/usr/bin/env python3
"""
Simple connectivity test script for Tesla API debugging.
This script tests various connectivity approaches to help diagnose issues.
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any

# Test different User-Agent strings
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# Test different endpoints
ENDPOINTS = [
    "https://www.tesla.com/inventory/api/v1/inventory-results",
    "https://tesla.com/inventory/api/v1/inventory-results",
    "https://www.tesla.com/inventory/api/v4/inventory-results",
    "https://www.tesla.com/tr_tr/inventory/api/v1/inventory-results"
]

# Basic Tesla domains to test
BASIC_URLS = [
    "https://www.tesla.com",
    "https://tesla.com",
    "https://www.tesla.com/tr_tr"
]

def get_headers(user_agent: str) -> Dict[str, str]:
    """Get headers for testing."""
    return {
        'User-Agent': user_agent,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Referer': 'https://www.tesla.com/',
        'Origin': 'https://www.tesla.com',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

def get_test_params() -> Dict[str, Any]:
    """Get test parameters for inventory API."""
    query_obj = {
        'model': 'my',
        'condition': 'new',
        'options': {},
        'arrangeby': 'Price',
        'order': 'asc',
        'market': 'TR',
        'language': 'tr',
        'super_region': 'europe',
        'lng': 28.9784,
        'lat': 41.0082,
        'zip': '34000',
        'range': 200
    }
    
    return {
        'query': json.dumps(query_obj),
        'offset': 0,
        'count': 10,
        'outsideOffset': 0,
        'outsideSearch': False
    }

async def test_basic_connectivity():
    """Test basic connectivity to Tesla domains."""
    print("🔍 Testing basic connectivity to Tesla domains...")

    timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=60.0)

    # Test with both HTTP/1.1 and HTTP/2
    for http2_enabled in [False, True]:
        protocol = "HTTP/2" if http2_enabled else "HTTP/1.1"
        print(f"  Testing with {protocol}...")

        for url in BASIC_URLS:
            for user_agent in USER_AGENTS[:2]:  # Test with first 2 user agents
                try:
                    headers = get_headers(user_agent)
                    async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=http2_enabled) as client:
                        print(f"    Trying {url} with {user_agent[:50]}...")
                        response = await client.get(url)
                        print(f"      ✅ Status: {response.status_code}")
                        if response.status_code in [200, 301, 302]:
                            print(f"      ✅ SUCCESS with {protocol}!")
                            return True
                except Exception as e:
                    print(f"      ❌ Error: {str(e)[:100]}")
                    continue

    return False

async def test_inventory_endpoints():
    """Test Tesla inventory API endpoints."""
    print("\n🔍 Testing Tesla inventory API endpoints...")

    params = get_test_params()
    timeout = httpx.Timeout(connect=15.0, read=45.0, write=10.0, pool=60.0)

    # Test with HTTP/1.1 first (more likely to work)
    for http2_enabled in [False, True]:
        protocol = "HTTP/2" if http2_enabled else "HTTP/1.1"
        print(f"  Testing with {protocol}...")

        for endpoint in ENDPOINTS:
            for user_agent in USER_AGENTS[:2]:  # Test with first 2 user agents
                try:
                    headers = get_headers(user_agent)
                    async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=http2_enabled) as client:
                        print(f"    Trying {endpoint}")
                        print(f"      User-Agent: {user_agent[:50]}...")

                        response = await client.get(endpoint, params=params)
                        print(f"      Status: {response.status_code}")

                        if response.status_code == 200:
                            try:
                                data = response.json()
                                results = data.get('results', [])
                                print(f"      ✅ SUCCESS! Found {len(results)} vehicles with {protocol}")
                                return True, endpoint, user_agent
                            except json.JSONDecodeError:
                                print(f"      ⚠️  Status 200 but invalid JSON")
                        elif response.status_code == 403:
                            print(f"      ❌ Forbidden (blocked)")
                        elif response.status_code == 429:
                            print(f"      ❌ Rate limited")
                        else:
                            print(f"      ❌ Unexpected status")

                        # Small delay between attempts
                        await asyncio.sleep(2)

                except httpx.TimeoutException:
                    print(f"      ❌ Timeout")
                except Exception as e:
                    print(f"      ❌ Error: {str(e)[:100]}")

                # Delay between user agent attempts
                await asyncio.sleep(1)

    return False, None, None

async def test_with_different_strategies():
    """Test with different request strategies."""
    print("\n🔍 Testing with different strategies...")
    
    # Strategy 1: Minimal headers
    print("  Strategy 1: Minimal headers")
    try:
        minimal_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
        }
        
        timeout = httpx.Timeout(connect=15.0, read=45.0)
        async with httpx.AsyncClient(timeout=timeout, headers=minimal_headers) as client:
            response = await client.get("https://www.tesla.com/inventory/api/v1/inventory-results", 
                                      params=get_test_params())
            print(f"    Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ SUCCESS with minimal headers! Found {len(data.get('results', []))} vehicles")
                return True
    except Exception as e:
        print(f"    ❌ Error: {str(e)[:100]}")
    
    # Strategy 2: No HTTP/2
    print("  Strategy 2: HTTP/1.1 only")
    try:
        headers = get_headers(USER_AGENTS[0])
        timeout = httpx.Timeout(connect=15.0, read=45.0)
        async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
            response = await client.get("https://www.tesla.com/inventory/api/v1/inventory-results", 
                                      params=get_test_params())
            print(f"    Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ SUCCESS with HTTP/1.1! Found {len(data.get('results', []))} vehicles")
                return True
    except Exception as e:
        print(f"    ❌ Error: {str(e)[:100]}")
    
    return False

async def main():
    """Main test function."""
    print("🚗 Tesla API Connectivity Test")
    print("=" * 50)
    
    start_time = time.time()
    
    # Test 1: Basic connectivity
    basic_ok = await test_basic_connectivity()
    if not basic_ok:
        print("❌ Basic connectivity failed. Check your internet connection.")
        return
    
    print("✅ Basic connectivity working")
    
    # Test 2: Inventory endpoints
    success, working_endpoint, working_ua = await test_inventory_endpoints()
    if success:
        print(f"\n🎉 SUCCESS!")
        print(f"Working endpoint: {working_endpoint}")
        print(f"Working User-Agent: {working_ua[:50]}...")
        return
    
    # Test 3: Different strategies
    strategy_success = await test_with_different_strategies()
    if strategy_success:
        print(f"\n🎉 SUCCESS with alternative strategy!")
        return
    
    # All tests failed
    print(f"\n❌ All tests failed")
    print("This suggests Tesla is blocking requests from your IP/region.")
    print("Possible solutions:")
    print("- Try from a different network (mobile hotspot)")
    print("- Use a VPN to a different location")
    print("- Wait and try again later")
    
    elapsed = time.time() - start_time
    print(f"\nTest completed in {elapsed:.1f} seconds")

if __name__ == "__main__":
    asyncio.run(main())
