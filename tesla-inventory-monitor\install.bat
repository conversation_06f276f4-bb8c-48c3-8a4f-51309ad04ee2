@echo off
REM Tesla Inventory Monitor - Windows Installation Script
REM This script installs Tesla Inventory Monitor on Windows 10/11

echo ========================================
echo Tesla Inventory Monitor - Installation
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.9 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Found Python %PYTHON_VERSION%

REM Check if version is 3.9 or higher (basic check)
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    if %%a LSS 3 (
        echo ERROR: Python 3.9 or higher is required
        pause
        exit /b 1
    )
    if %%a EQU 3 if %%b LSS 9 (
        echo ERROR: Python 3.9 or higher is required
        pause
        exit /b 1
    )
)

echo Python version is compatible
echo.

REM Create virtual environment
echo Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

REM Install the application
echo Installing Tesla Inventory Monitor...
pip install -e .
if errorlevel 1 (
    echo ERROR: Failed to install application
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Initialize configuration: tesla-monitor init
echo 2. Edit configuration file: config.json
echo 3. Test connection: tesla-monitor test
echo 4. Start monitoring: tesla-monitor start
echo.
echo To use Tesla Monitor, activate the virtual environment first:
echo   venv\Scripts\activate.bat
echo   tesla-monitor --help
echo.
echo For more information, see README.md
echo.
pause
