#!/usr/bin/env python3
"""
Test Turkish Tesla endpoints specifically.
Since the user confirmed they can access https://www.tesla.com/tr_tr/ from Chrome,
let's test the Turkish inventory API endpoints.
"""

import asyncio
import httpx
import json
import time

# Turkish-specific endpoints to test
TURKISH_ENDPOINTS = [
    "https://www.tesla.com/tr_tr/inventory/api/v1/inventory-results",
    "https://www.tesla.com/tr_tr/inventory/api/v4/inventory-results",
    "https://www.tesla.com/inventory/api/v1/inventory-results",
]

def get_chrome_headers():
    """Get headers that match Chrome browser from Turkey."""
    return {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Referer': 'https://www.tesla.com/tr_tr/inventory/new/my',
        'Origin': 'https://www.tesla.com',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
    }

def get_turkish_inventory_params():
    """Get parameters for Turkish Tesla inventory."""
    query_obj = {
        'model': 'my',  # Model Y
        'condition': 'new',
        'options': {},
        'arrangeby': 'Price',
        'order': 'asc',
        'market': 'TR',  # Turkey market
        'language': 'tr',  # Turkish language
        'super_region': 'europe',
        'lng': 28.9784,  # Istanbul longitude
        'lat': 41.0082,  # Istanbul latitude
        'zip': '34000',  # Istanbul postal code
        'range': 200,
        'isHomeDelivery': True,
        'isTestDrive': True
    }
    
    return {
        'query': json.dumps(query_obj),
        'offset': 0,
        'count': 50,
        'outsideOffset': 0,
        'outsideSearch': False
    }

async def test_basic_turkish_access():
    """Test basic access to Turkish Tesla site."""
    print("🇹🇷 Testing basic access to Turkish Tesla site...")
    
    timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=60.0)
    headers = get_chrome_headers()
    
    try:
        async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
            response = await client.get("https://www.tesla.com/tr_tr/")
            print(f"  Status: {response.status_code}")
            if response.status_code == 200:
                print("  ✅ Successfully accessed Turkish Tesla site!")
                return True
            else:
                print(f"  ❌ Unexpected status: {response.status_code}")
                return False
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        return False

async def test_turkish_inventory_page():
    """Test access to Turkish inventory page."""
    print("\n🚗 Testing Turkish inventory page...")
    
    timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=60.0)
    headers = get_chrome_headers()
    
    try:
        async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
            response = await client.get("https://www.tesla.com/tr_tr/inventory/new/my")
            print(f"  Status: {response.status_code}")
            if response.status_code == 200:
                print("  ✅ Successfully accessed Turkish inventory page!")
                return True
            else:
                print(f"  ❌ Unexpected status: {response.status_code}")
                return False
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        return False

async def test_turkish_api_endpoints():
    """Test Turkish Tesla API endpoints."""
    print("\n🔍 Testing Turkish Tesla API endpoints...")
    
    params = get_turkish_inventory_params()
    timeout = httpx.Timeout(connect=15.0, read=45.0, write=10.0, pool=60.0)
    headers = get_chrome_headers()
    
    for endpoint in TURKISH_ENDPOINTS:
        print(f"\n  Testing: {endpoint}")
        
        try:
            async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
                response = await client.get(endpoint, params=params)
                print(f"    Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        results = data.get('results', [])
                        print(f"    ✅ SUCCESS! Found {len(results)} vehicles")
                        
                        # Show first vehicle details if available
                        if results:
                            first_vehicle = results[0]
                            print(f"    📋 Sample vehicle: {first_vehicle.get('Model', 'Unknown')} - {first_vehicle.get('TrimName', 'Unknown')} - €{first_vehicle.get('Price', 'Unknown')}")
                        
                        return True, endpoint
                        
                    except json.JSONDecodeError as e:
                        print(f"    ⚠️  Status 200 but invalid JSON: {e}")
                        print(f"    Response preview: {response.text[:200]}...")
                        
                elif response.status_code == 403:
                    print(f"    ❌ Forbidden - possible blocking")
                elif response.status_code == 429:
                    print(f"    ❌ Rate limited")
                elif response.status_code == 404:
                    print(f"    ❌ Not found - endpoint may not exist")
                else:
                    print(f"    ❌ Unexpected status")
                    print(f"    Response preview: {response.text[:200]}...")
                    
        except httpx.TimeoutException:
            print(f"    ❌ Timeout")
        except Exception as e:
            print(f"    ❌ Error: {str(e)}")
        
        # Small delay between attempts
        await asyncio.sleep(2)
    
    return False, None

async def main():
    """Main test function."""
    print("🇹🇷 Tesla Turkish Endpoint Test")
    print("=" * 50)
    
    start_time = time.time()
    
    # Test 1: Basic Turkish site access
    basic_ok = await test_basic_turkish_access()
    if not basic_ok:
        print("❌ Cannot access basic Turkish Tesla site")
        return
    
    # Test 2: Turkish inventory page
    inventory_page_ok = await test_turkish_inventory_page()
    if not inventory_page_ok:
        print("❌ Cannot access Turkish inventory page")
        return
    
    # Test 3: Turkish API endpoints
    api_success, working_endpoint = await test_turkish_api_endpoints()
    
    if api_success:
        print(f"\n🎉 SUCCESS!")
        print(f"Working Turkish endpoint: {working_endpoint}")
        print("\nThis endpoint should work in the Tesla Inventory Monitor!")
    else:
        print(f"\n❌ All Turkish API endpoints failed")
        print("The inventory page works but the API endpoints are not accessible.")
        print("This might be due to:")
        print("- Different authentication requirements")
        print("- Missing required headers or cookies")
        print("- API endpoints requiring different parameters")
    
    elapsed = time.time() - start_time
    print(f"\nTest completed in {elapsed:.1f} seconds")

if __name__ == "__main__":
    asyncio.run(main())
