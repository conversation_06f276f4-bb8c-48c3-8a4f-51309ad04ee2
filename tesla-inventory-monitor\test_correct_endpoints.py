#!/usr/bin/env python3
"""
Test the correct Tesla API endpoints based on user feedback.
The user confirmed:
- https://www.tesla.com/tr_tr/inventory/api/v1/inventory-results gives 404
- https://www.tesla.com/tr_tr/inventory/new/my is accessible

So we need to test the standard API endpoints with Turkish parameters.
"""

import asyncio
import httpx
import json
import time

# Correct API endpoints (without /tr_tr/ prefix)
CORRECT_ENDPOINTS = [
    "https://www.tesla.com/inventory/api/v1/inventory-results",
    "https://www.tesla.com/inventory/api/v4/inventory-results",
    "https://tesla.com/inventory/api/v1/inventory-results",
]

def get_turkish_headers():
    """Get headers that match Turkish Tesla page."""
    return {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Referer': 'https://www.tesla.com/tr_tr/inventory/new/my',
        'Origin': 'https://www.tesla.com',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
    }

def get_turkish_params():
    """Get correct parameters for Turkish market."""
    query_obj = {
        'model': 'my',  # Model Y
        'condition': 'new',
        'options': {},
        'arrangeby': 'Price',
        'order': 'asc',
        'market': 'TR',  # Turkey market code
        'language': 'tr',  # Turkish language
        'super_region': 'europe',  # Europe region
        'lng': 28.9784,  # Istanbul longitude
        'lat': 41.0082,  # Istanbul latitude
        'zip': '34000',  # Istanbul postal code
        'range': 200,
        'isHomeDelivery': True,
        'isTestDrive': True
    }
    
    return {
        'query': json.dumps(query_obj),
        'offset': 0,
        'count': 50,
        'outsideOffset': 0,
        'outsideSearch': False
    }

async def test_basic_connectivity():
    """Test basic connectivity first."""
    print("🔍 Testing basic connectivity...")
    
    timeout = httpx.Timeout(connect=10.0, read=30.0, write=10.0, pool=60.0)
    headers = get_turkish_headers()
    
    test_urls = [
        "https://www.google.com",  # Control test
        "https://www.tesla.com",
        "https://www.tesla.com/tr_tr/inventory/new/my"  # User confirmed this works
    ]
    
    for url in test_urls:
        print(f"  Testing: {url}")
        try:
            async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
                response = await client.get(url)
                print(f"    ✅ Status: {response.status_code}")
                if response.status_code in [200, 301, 302]:
                    if "tesla" in url:
                        print(f"    ✅ Tesla connectivity confirmed!")
                        return True
        except Exception as e:
            print(f"    ❌ Error: {str(e)[:100]}")
    
    return False

async def test_correct_api_endpoints():
    """Test the correct API endpoints."""
    print("\n🔍 Testing correct Tesla API endpoints...")
    
    params = get_turkish_params()
    timeout = httpx.Timeout(connect=15.0, read=45.0, write=10.0, pool=60.0)
    headers = get_turkish_headers()
    
    print(f"Using parameters: {json.dumps(params, indent=2)}")
    
    for endpoint in CORRECT_ENDPOINTS:
        print(f"\n  Testing: {endpoint}")
        
        try:
            async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
                response = await client.get(endpoint, params=params)
                print(f"    Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        results = data.get('results', [])
                        print(f"    ✅ SUCCESS! Found {len(results)} vehicles")
                        
                        # Show sample vehicle data
                        if results:
                            sample = results[0]
                            model = sample.get('Model', 'Unknown')
                            trim = sample.get('TrimName', 'Unknown')
                            price = sample.get('Price', 'Unknown')
                            print(f"    📋 Sample: {model} {trim} - €{price}")
                        
                        return True, endpoint
                        
                    except json.JSONDecodeError as e:
                        print(f"    ⚠️  Status 200 but invalid JSON: {e}")
                        print(f"    Response preview: {response.text[:300]}...")
                        
                elif response.status_code == 403:
                    print(f"    ❌ Forbidden (403) - possible blocking")
                elif response.status_code == 429:
                    print(f"    ❌ Rate limited (429)")
                elif response.status_code == 404:
                    print(f"    ❌ Not found (404) - endpoint doesn't exist")
                else:
                    print(f"    ❌ Unexpected status: {response.status_code}")
                    print(f"    Response preview: {response.text[:200]}...")
                    
        except httpx.TimeoutException:
            print(f"    ❌ Timeout")
        except Exception as e:
            print(f"    ❌ Error: {str(e)[:100]}")
        
        # Delay between attempts
        await asyncio.sleep(2)
    
    return False, None

async def test_alternative_approaches():
    """Test alternative approaches if standard endpoints fail."""
    print("\n🔍 Testing alternative approaches...")
    
    # Try with minimal parameters
    print("  Trying with minimal parameters...")
    minimal_query = {
        'model': 'my',
        'condition': 'new',
        'market': 'TR',
        'language': 'tr'
    }
    
    minimal_params = {
        'query': json.dumps(minimal_query),
        'offset': 0,
        'count': 10
    }
    
    timeout = httpx.Timeout(connect=15.0, read=45.0, write=10.0, pool=60.0)
    headers = get_turkish_headers()
    
    try:
        async with httpx.AsyncClient(timeout=timeout, headers=headers, http2=False) as client:
            response = await client.get(
                "https://www.tesla.com/inventory/api/v1/inventory-results", 
                params=minimal_params
            )
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                print(f"    ✅ SUCCESS with minimal params! Found {len(results)} vehicles")
                return True
            else:
                print(f"    Response: {response.text[:200]}...")
                
    except Exception as e:
        print(f"    ❌ Error: {str(e)[:100]}")
    
    return False

async def main():
    """Main test function."""
    print("🚗 Tesla Correct Endpoint Test")
    print("=" * 50)
    
    start_time = time.time()
    
    # Test 1: Basic connectivity
    basic_ok = await test_basic_connectivity()
    if not basic_ok:
        print("❌ Basic connectivity failed")
        return
    
    print("✅ Basic connectivity working")
    
    # Test 2: Correct API endpoints
    api_success, working_endpoint = await test_correct_api_endpoints()
    
    if api_success:
        print(f"\n🎉 SUCCESS!")
        print(f"Working endpoint: {working_endpoint}")
        print("\nThis endpoint should work in the Tesla Inventory Monitor!")
    else:
        print(f"\n⚠️  Standard endpoints failed, trying alternatives...")
        
        # Test 3: Alternative approaches
        alt_success = await test_alternative_approaches()
        
        if alt_success:
            print(f"\n🎉 SUCCESS with alternative approach!")
        else:
            print(f"\n❌ All approaches failed")
            print("\nPossible issues:")
            print("- Tesla requires authentication/cookies")
            print("- Different API version or parameters needed")
            print("- Rate limiting or IP blocking")
            print("- API access restricted to browser requests only")
    
    elapsed = time.time() - start_time
    print(f"\nTest completed in {elapsed:.1f} seconds")

if __name__ == "__main__":
    asyncio.run(main())
