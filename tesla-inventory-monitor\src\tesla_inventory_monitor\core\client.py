"""Tesla API client for direct inventory access with enhanced connectivity."""

import asyncio
import json
import random
import time
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urljoin, quote
import httpx
from loguru import logger

from ..config.settings import Settings
from ..models.vehicle import Vehicle


class TeslaApiError(Exception):
    """Custom exception for Tesla API errors."""
    pass


class TeslaInventoryClient:
    """Async Tesla API client for inventory operations with enhanced anti-bot bypass."""

    # Multiple User-Agent strings to rotate through
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    ]

    # Alternative Tesla endpoints to try (Turkish endpoints first since they're confirmed working)
    TESLA_ENDPOINTS = [
        "https://www.tesla.com/tr_tr/inventory/api/v1/inventory-results",  # Turkish endpoint first
        "https://www.tesla.com/inventory/api/v1/inventory-results",
        "https://tesla.com/inventory/api/v1/inventory-results",
        "https://www.tesla.com/inventory/api/v4/inventory-results",
        "https://www.tesla.com/inventory/api/v3/inventory-results"
    ]

    # Regional Tesla domains to try
    REGIONAL_DOMAINS = [
        "https://www.tesla.com",
        "https://tesla.com",
        "https://www.tesla.com/tr_tr",
        "https://www.tesla.com/en_eu"
    ]

    def __init__(self, settings: Settings):
        self.settings = settings
        self.session: Optional[httpx.AsyncClient] = None
        self.current_user_agent = random.choice(self.USER_AGENTS)
        self.request_count = 0
        self.last_request_time = 0

        # Enhanced headers with randomization
        self.base_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8,de;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.tesla.com/',
            'Origin': 'https://www.tesla.com',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()

    def _get_current_headers(self) -> Dict[str, str]:
        """Get current headers with randomized User-Agent."""
        headers = self.base_headers.copy()
        headers['User-Agent'] = self.current_user_agent

        # Add some randomization to make requests look more human
        if random.random() < 0.3:  # 30% chance to add extra headers
            headers['X-Requested-With'] = 'XMLHttpRequest'

        if random.random() < 0.2:  # 20% chance to modify accept header
            headers['Accept'] = 'application/json'

        return headers

    def _rotate_user_agent(self) -> None:
        """Rotate to a different User-Agent string."""
        self.current_user_agent = random.choice(self.USER_AGENTS)
        logger.debug(f"Rotated to new User-Agent: {self.current_user_agent[:50]}...")

    async def start_session(self) -> None:
        """Initialize the HTTP session with enhanced settings."""
        if self.session is None:
            # Longer timeout for better reliability
            timeout = httpx.Timeout(
                connect=15.0,
                read=45.0,
                write=10.0,
                pool=60.0
            )

            # Build client configuration
            client_config = {
                'headers': self._get_current_headers(),
                'timeout': timeout,
                'follow_redirects': True,
                'limits': httpx.Limits(
                    max_connections=self.settings.monitoring.max_concurrent_requests,
                    max_keepalive_connections=2
                ),
                'http2': self.settings.api.enable_http2,
                'verify': True,  # SSL verification
            }

            # Add proxy support if configured
            if hasattr(self.settings.api, 'proxy_url') and self.settings.api.proxy_url:
                client_config['proxies'] = self.settings.api.proxy_url
                logger.info(f"Using proxy: {self.settings.api.proxy_url}")

            self.session = httpx.AsyncClient(**client_config)
            logger.debug(f"Tesla API client session started (HTTP/2: {self.settings.api.enable_http2})")

    async def close_session(self) -> None:
        """Close the HTTP session."""
        if self.session:
            await self.session.aclose()
            self.session = None
            logger.debug("Tesla API client session closed")

    async def _human_like_delay(self) -> None:
        """Add human-like delay between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        # Minimum delay between requests (1-3 seconds)
        min_delay = random.uniform(1.0, 3.0)

        if time_since_last < min_delay:
            delay = min_delay - time_since_last
            await asyncio.sleep(delay)

        self.last_request_time = time.time()
        self.request_count += 1

        # Rotate User-Agent every 5-10 requests
        if self.request_count % random.randint(5, 10) == 0:
            self._rotate_user_agent()
            # Recreate session with new headers
            await self.close_session()
            await self.start_session()

    async def _make_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """Make HTTP request with enhanced retry logic and anti-bot measures."""
        if not self.session:
            await self.start_session()

        # Add human-like delay
        await self._human_like_delay()

        last_exception = None
        last_error_details = "Unknown error"

        for attempt in range(self.settings.api.max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")

                # Update headers for each request
                if 'headers' not in kwargs:
                    kwargs['headers'] = {}
                kwargs['headers'].update(self._get_current_headers())

                response = await self.session.request(method, url, **kwargs)

                logger.debug(f"Response status: {response.status_code}")

                # Check for rate limiting or blocking
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    logger.warning(f"Rate limited. Waiting {retry_after} seconds...")
                    await asyncio.sleep(retry_after)
                    continue

                if response.status_code == 403:
                    logger.warning("Access forbidden. Rotating User-Agent and retrying...")
                    self._rotate_user_agent()
                    await self.close_session()
                    await self.start_session()
                    continue

                # Apply additional rate limiting
                if self.settings.api.rate_limit_delay > 0:
                    await asyncio.sleep(random.uniform(
                        self.settings.api.rate_limit_delay * 0.5,
                        self.settings.api.rate_limit_delay * 1.5
                    ))

                return response

            except httpx.TimeoutException as e:
                last_exception = e
                last_error_details = f"Timeout: {str(e)}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")

            except httpx.ConnectError as e:
                last_exception = e
                last_error_details = f"Connection error: {str(e)}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")

            except httpx.HTTPStatusError as e:
                last_exception = e
                last_error_details = f"HTTP {e.response.status_code}: {e.response.text[:200]}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")

            except httpx.RequestError as e:
                last_exception = e
                last_error_details = f"Request error: {str(e)} (Type: {type(e).__name__})"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")

            except Exception as e:
                last_exception = e
                last_error_details = f"Unexpected error: {str(e)} (Type: {type(e).__name__})"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")

            # Enhanced retry with exponential backoff and jitter
            if attempt < self.settings.api.max_retries - 1:
                base_delay = self.settings.api.retry_delay_seconds * (2 ** attempt)
                jitter = random.uniform(0.5, 1.5)
                delay = base_delay * jitter
                logger.info(f"Retrying in {delay:.1f} seconds...")
                await asyncio.sleep(delay)

        # All retries exhausted
        error_msg = f"Request failed after {self.settings.api.max_retries} attempts. Last error: {last_error_details}"
        logger.error(error_msg)
        raise TeslaApiError(error_msg)
    
    def _build_inventory_params(self) -> Dict[str, Any]:
        """Build query parameters for Tesla inventory API with enhanced regional support."""
        # Enhanced query object for Turkey market
        query_obj = {
            'model': self.settings.filters.model,
            'condition': self.settings.filters.condition[0] if self.settings.filters.condition else 'new',
            'options': {},
            'arrangeby': 'Price',
            'order': 'asc',
            'market': self.settings.filters.market,
            'language': 'tr',  # Turkish language for Turkey market
            'super_region': 'europe',  # Turkey is in European region for Tesla
            'lng': 28.9784,  # Istanbul longitude
            'lat': 41.0082,  # Istanbul latitude
            'zip': '34000',  # Istanbul postal code
            'range': 200,  # Increased search range
            'isHomeDelivery': True,
            'isTestDrive': True
        }

        # Add price filters to query if specified
        if self.settings.filters.price_min:
            query_obj['options']['PRICE'] = [str(self.settings.filters.price_min)]

        if self.settings.filters.price_max:
            if 'PRICE' not in query_obj['options']:
                query_obj['options']['PRICE'] = []
            query_obj['options']['PRICE'].append(str(self.settings.filters.price_max))

        # Add year filters
        if self.settings.filters.year_min:
            query_obj['options']['YEAR'] = [str(self.settings.filters.year_min)]

        params = {
            'query': json.dumps(query_obj),
            'offset': 0,
            'count': 50,
            'outsideOffset': 0,
            'outsideSearch': False
        }

        return params

    async def _try_multiple_endpoints(self, params: Dict[str, Any]) -> Tuple[httpx.Response, str]:
        """Try multiple Tesla endpoints until one works."""
        last_error = None

        # Try different endpoints (now full URLs)
        for url in self.TESLA_ENDPOINTS:
            try:
                logger.info(f"Trying endpoint: {url}")

                response = await self._make_request('GET', url, params=params)

                if response.status_code == 200:
                    logger.info(f"Successfully connected to: {url}")
                    return response, url
                elif response.status_code in [403, 429]:
                    logger.warning(f"Endpoint {url} returned {response.status_code}, trying next...")
                    # Add longer delay for rate limiting
                    await asyncio.sleep(3)
                    continue
                else:
                    logger.warning(f"Endpoint {url} returned unexpected status {response.status_code}")

            except Exception as e:
                last_error = e
                logger.warning(f"Failed to connect to {url}: {str(e)}")
                # Add delay between failed attempts
                await asyncio.sleep(2)
                continue

        # If all endpoints failed, raise the last error
        if last_error:
            raise last_error
        else:
            raise TeslaApiError("All Tesla endpoints failed to respond")
    
    async def get_inventory(self) -> List[Vehicle]:
        """Fetch Tesla inventory with multiple endpoint fallback strategy."""
        try:
            # Build query parameters
            params = self._build_inventory_params()
            logger.debug(f"Query parameters: {params}")

            # Try multiple endpoints until one works
            response, successful_url = await self._try_multiple_endpoints(params)

            # Handle response
            if response.status_code == 200:
                try:
                    data = response.json()
                    raw_vehicles = data.get('results', [])

                    logger.info(f"Received {len(raw_vehicles)} vehicles from Tesla API ({successful_url})")

                    # Convert to Vehicle objects
                    vehicles = []
                    for raw_vehicle in raw_vehicles:
                        try:
                            vehicle = Vehicle.from_tesla_api(raw_vehicle)
                            vehicles.append(vehicle)
                        except Exception as e:
                            logger.warning(f"Failed to parse vehicle data: {e}")
                            logger.debug(f"Raw vehicle data: {raw_vehicle}")
                            continue

                    logger.info(f"Successfully parsed {len(vehicles)} vehicles")
                    return vehicles

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse Tesla API response as JSON: {e}")
                    logger.debug(f"Response content: {response.text[:500]}")
                    raise TeslaApiError(f"Invalid JSON response: {e}")

            elif response.status_code == 429:
                logger.warning("Rate limited by Tesla API")
                raise TeslaApiError("Rate limited by Tesla API")

            elif response.status_code == 403:
                logger.error("Access forbidden by Tesla API - possible blocking")
                raise TeslaApiError("Access forbidden by Tesla API")

            elif response.status_code == 404:
                logger.error("Tesla API endpoint not found")
                raise TeslaApiError("Tesla API endpoint not found")

            else:
                logger.error(f"Tesla API returned status {response.status_code}")
                logger.debug(f"Response content: {response.text[:500]}")
                raise TeslaApiError(f"API request failed with status {response.status_code}")

        except TeslaApiError:
            # Re-raise Tesla API errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching inventory: {e}")
            raise TeslaApiError(f"Unexpected error: {e}")
    
    async def test_basic_connectivity(self) -> bool:
        """Test basic connectivity to Tesla website."""
        try:
            logger.info("Testing basic connectivity to Tesla...")

            # Try to reach Tesla's main page first
            for domain in ["https://www.tesla.com", "https://tesla.com"]:
                try:
                    response = await self._make_request('GET', domain)
                    if response.status_code in [200, 301, 302]:
                        logger.info(f"Basic connectivity to {domain} successful")
                        return True
                except Exception as e:
                    logger.warning(f"Failed to connect to {domain}: {e}")
                    continue

            return False
        except Exception as e:
            logger.error(f"Basic connectivity test failed: {e}")
            return False

    async def test_connection(self) -> bool:
        """Test connection to Tesla API with comprehensive checks."""
        try:
            logger.info("Testing Tesla API connection...")

            # First test basic connectivity
            if not await self.test_basic_connectivity():
                logger.error("Basic connectivity test failed")
                return False

            # Then test inventory API
            vehicles = await self.get_inventory()
            logger.info(f"Tesla API connection test successful - found {len(vehicles)} vehicles")
            return True

        except TeslaApiError as e:
            logger.error(f"Tesla API connection test failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Tesla API connection test failed with unexpected error: {e}")
            return False
    
    async def get_inventory_with_filters(self) -> List[Vehicle]:
        """Get inventory and apply local filters."""
        vehicles = await self.get_inventory()
        
        # Apply local filters
        filtered_vehicles = []
        for vehicle in vehicles:
            # Price filters
            if self.settings.filters.price_min and vehicle.price < self.settings.filters.price_min:
                continue
            if self.settings.filters.price_max and vehicle.price > self.settings.filters.price_max:
                continue
            
            # Year filters
            if self.settings.filters.year_min and vehicle.year < self.settings.filters.year_min:
                continue
            if self.settings.filters.year_max and vehicle.year > self.settings.filters.year_max:
                continue
            
            # Condition filter
            if (self.settings.filters.condition and 
                vehicle.condition.value not in [c.lower() for c in self.settings.filters.condition]):
                continue
            
            # Trim filter
            if (self.settings.filters.trim_levels and 
                vehicle.trim not in self.settings.filters.trim_levels):
                continue
            
            # Color filter
            if self.settings.filters.colors:
                vehicle_colors = [vehicle.exterior_color, vehicle.interior_color]
                if not any(color in self.settings.filters.colors for color in vehicle_colors if color):
                    continue
            
            filtered_vehicles.append(vehicle)
        
        logger.info(f"Filtered {len(vehicles)} vehicles down to {len(filtered_vehicles)} matching criteria")
        return filtered_vehicles
