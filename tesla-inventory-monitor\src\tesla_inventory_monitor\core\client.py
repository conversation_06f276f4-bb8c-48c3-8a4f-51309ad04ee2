"""Tesla API client for direct inventory access."""

import asyncio
import json
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin
import httpx
from loguru import logger

from ..config.settings import Settings
from ..models.vehicle import Vehicle


class TeslaApiError(Exception):
    """Custom exception for Tesla API errors."""
    pass


class TeslaInventoryClient:
    """Async Tesla API client for inventory operations."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.session: Optional[httpx.AsyncClient] = None
        
        # Build headers to mimic a real browser
        self.headers = {
            'User-Agent': settings.api.user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.tesla.com/',
            'Origin': 'https://www.tesla.com',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_session()
    
    async def start_session(self) -> None:
        """Initialize the HTTP session."""
        if self.session is None:
            timeout = httpx.Timeout(self.settings.api.timeout_seconds)
            self.session = httpx.AsyncClient(
                headers=self.headers,
                timeout=timeout,
                follow_redirects=True,
                limits=httpx.Limits(max_connections=self.settings.monitoring.max_concurrent_requests)
            )
            logger.debug("Tesla API client session started")
    
    async def close_session(self) -> None:
        """Close the HTTP session."""
        if self.session:
            await self.session.aclose()
            self.session = None
            logger.debug("Tesla API client session closed")
    
    async def _make_request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """Make HTTP request with retry logic and error handling."""
        if not self.session:
            await self.start_session()
        
        last_exception = None
        last_error_details = "Unknown error"
        
        for attempt in range(self.settings.api.max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = await self.session.request(method, url, **kwargs)
                
                logger.debug(f"Response status: {response.status_code}")
                
                # Apply rate limiting
                if self.settings.api.rate_limit_delay > 0:
                    await asyncio.sleep(self.settings.api.rate_limit_delay)
                
                return response
                
            except httpx.TimeoutException as e:
                last_exception = e
                last_error_details = f"Timeout after {self.settings.api.timeout_seconds}s: {str(e)}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")
            
            except httpx.ConnectError as e:
                last_exception = e
                last_error_details = f"Connection error: {str(e)}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")
            
            except httpx.HTTPStatusError as e:
                last_exception = e
                last_error_details = f"HTTP {e.response.status_code}: {e.response.text[:200]}"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")
            
            except httpx.RequestError as e:
                last_exception = e
                last_error_details = f"Request error: {str(e)} (Type: {type(e).__name__})"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")
            
            except Exception as e:
                last_exception = e
                last_error_details = f"Unexpected error: {str(e)} (Type: {type(e).__name__})"
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.settings.api.max_retries}): {last_error_details}")
            
            # Retry with exponential backoff
            if attempt < self.settings.api.max_retries - 1:
                delay = self.settings.api.retry_delay_seconds * (2 ** attempt)
                logger.info(f"Retrying in {delay} seconds...")
                await asyncio.sleep(delay)
        
        # All retries exhausted
        error_msg = f"Request failed after {self.settings.api.max_retries} attempts. Last error: {last_error_details}"
        logger.error(error_msg)
        raise TeslaApiError(error_msg)
    
    def _build_inventory_params(self) -> Dict[str, Any]:
        """Build query parameters for Tesla inventory API."""
        # Build the query object that Tesla expects
        query_obj = {
            'model': self.settings.filters.model,
            'condition': self.settings.filters.condition[0] if self.settings.filters.condition else 'new',
            'options': {},
            'arrangeby': 'Price',
            'order': 'asc',
            'market': self.settings.filters.market,
            'language': 'en',
            'super_region': 'north_america',
            'lng': 28.9784,  # Istanbul longitude
            'lat': 41.0082,  # Istanbul latitude
            'zip': '34000',  # Istanbul postal code
            'range': 0
        }
        
        # Add price filters to query if specified
        if self.settings.filters.price_min:
            query_obj['options']['PRICE'] = [str(self.settings.filters.price_min)]
        
        params = {
            'query': json.dumps(query_obj),
            'offset': 0,
            'count': 50,
            'outsideOffset': 0,
            'outsideSearch': False
        }
        
        return params
    
    async def get_inventory(self) -> List[Vehicle]:
        """Fetch Tesla inventory and convert to Vehicle objects."""
        try:
            # Build the full URL
            url = urljoin(self.settings.api.base_url, self.settings.api.inventory_endpoint)
            
            # Build query parameters
            params = self._build_inventory_params()
            
            logger.debug(f"Fetching inventory from {url}")
            logger.debug(f"Query parameters: {params}")
            
            # Make the request
            response = await self._make_request('GET', url, params=params)
            
            # Handle response
            if response.status_code == 200:
                try:
                    data = response.json()
                    raw_vehicles = data.get('results', [])
                    
                    logger.info(f"Received {len(raw_vehicles)} vehicles from Tesla API")
                    
                    # Convert to Vehicle objects
                    vehicles = []
                    for raw_vehicle in raw_vehicles:
                        try:
                            vehicle = Vehicle.from_tesla_api(raw_vehicle)
                            vehicles.append(vehicle)
                        except Exception as e:
                            logger.warning(f"Failed to parse vehicle data: {e}")
                            logger.debug(f"Raw vehicle data: {raw_vehicle}")
                            continue
                    
                    logger.info(f"Successfully parsed {len(vehicles)} vehicles")
                    return vehicles
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse Tesla API response as JSON: {e}")
                    logger.debug(f"Response content: {response.text[:500]}")
                    raise TeslaApiError(f"Invalid JSON response: {e}")
            
            elif response.status_code == 429:
                logger.warning("Rate limited by Tesla API")
                raise TeslaApiError("Rate limited by Tesla API")
            
            elif response.status_code == 403:
                logger.error("Access forbidden by Tesla API - possible blocking")
                raise TeslaApiError("Access forbidden by Tesla API")
            
            elif response.status_code == 404:
                logger.error("Tesla API endpoint not found")
                raise TeslaApiError("Tesla API endpoint not found")
            
            else:
                logger.error(f"Tesla API returned status {response.status_code}")
                logger.debug(f"Response content: {response.text[:500]}")
                raise TeslaApiError(f"API request failed with status {response.status_code}")
        
        except TeslaApiError:
            # Re-raise Tesla API errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching inventory: {e}")
            raise TeslaApiError(f"Unexpected error: {e}")
    
    async def test_connection(self) -> bool:
        """Test connection to Tesla API."""
        try:
            logger.info("Testing Tesla API connection...")
            vehicles = await self.get_inventory()
            logger.info(f"Tesla API connection test successful - found {len(vehicles)} vehicles")
            return True
        except Exception as e:
            logger.error(f"Tesla API connection test failed: {e}")
            return False
    
    async def get_inventory_with_filters(self) -> List[Vehicle]:
        """Get inventory and apply local filters."""
        vehicles = await self.get_inventory()
        
        # Apply local filters
        filtered_vehicles = []
        for vehicle in vehicles:
            # Price filters
            if self.settings.filters.price_min and vehicle.price < self.settings.filters.price_min:
                continue
            if self.settings.filters.price_max and vehicle.price > self.settings.filters.price_max:
                continue
            
            # Year filters
            if self.settings.filters.year_min and vehicle.year < self.settings.filters.year_min:
                continue
            if self.settings.filters.year_max and vehicle.year > self.settings.filters.year_max:
                continue
            
            # Condition filter
            if (self.settings.filters.condition and 
                vehicle.condition.value not in [c.lower() for c in self.settings.filters.condition]):
                continue
            
            # Trim filter
            if (self.settings.filters.trim_levels and 
                vehicle.trim not in self.settings.filters.trim_levels):
                continue
            
            # Color filter
            if self.settings.filters.colors:
                vehicle_colors = [vehicle.exterior_color, vehicle.interior_color]
                if not any(color in self.settings.filters.colors for color in vehicle_colors if color):
                    continue
            
            filtered_vehicles.append(vehicle)
        
        logger.info(f"Filtered {len(vehicles)} vehicles down to {len(filtered_vehicles)} matching criteria")
        return filtered_vehicles
