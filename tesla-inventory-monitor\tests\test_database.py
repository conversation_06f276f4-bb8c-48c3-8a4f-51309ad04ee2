"""Tests for database functionality."""

import pytest
from datetime import datetime, timedelta

from tesla_inventory_monitor.models.vehicle import Vehicle, VehicleStatus
from tesla_inventory_monitor.models.database import DatabaseManager


class TestDatabaseManager:
    """Test DatabaseManager functionality."""
    
    @pytest.mark.asyncio
    async def test_database_initialization(self, test_db):
        """Test database initialization."""
        # Database should be initialized by fixture
        stats = await test_db.get_statistics()
        assert stats['total_vehicles'] == 0
    
    @pytest.mark.asyncio
    async def test_save_and_get_vehicle(self, test_db, sample_vehicle):
        """Test saving and retrieving a vehicle."""
        # Save vehicle
        await test_db.save_vehicle(sample_vehicle)
        
        # Retrieve vehicle
        retrieved = await test_db.get_vehicle(sample_vehicle.vin)
        
        assert retrieved is not None
        assert retrieved.vin == sample_vehicle.vin
        assert retrieved.model == sample_vehicle.model
        assert retrieved.year == sample_vehicle.year
        assert retrieved.price == sample_vehicle.price
    
    @pytest.mark.asyncio
    async def test_update_existing_vehicle(self, test_db, sample_vehicle):
        """Test updating an existing vehicle."""
        # Save initial vehicle
        await test_db.save_vehicle(sample_vehicle)
        
        # Update vehicle
        sample_vehicle.price = 2000000
        sample_vehicle.update_last_seen()
        await test_db.save_vehicle(sample_vehicle)
        
        # Retrieve and verify update
        retrieved = await test_db.get_vehicle(sample_vehicle.vin)
        assert retrieved.price == 2000000
        assert retrieved.status == VehicleStatus.SEEN
    
    @pytest.mark.asyncio
    async def test_get_all_vehicles(self, test_db, sample_vehicle):
        """Test getting all vehicles."""
        # Initially empty
        vehicles = await test_db.get_all_vehicles()
        assert len(vehicles) == 0
        
        # Add vehicle
        await test_db.save_vehicle(sample_vehicle)
        
        # Should have one vehicle
        vehicles = await test_db.get_all_vehicles()
        assert len(vehicles) == 1
        assert vehicles[0].vin == sample_vehicle.vin
    
    @pytest.mark.asyncio
    async def test_get_vehicles_by_status(self, test_db, sample_vehicle):
        """Test getting vehicles by status."""
        # Save vehicle with NEW status
        await test_db.save_vehicle(sample_vehicle)
        
        # Get new vehicles
        new_vehicles = await test_db.get_vehicles_by_status(VehicleStatus.NEW)
        assert len(new_vehicles) == 1
        
        # Get sold vehicles (should be empty)
        sold_vehicles = await test_db.get_vehicles_by_status(VehicleStatus.SOLD)
        assert len(sold_vehicles) == 0
        
        # Update status and test again
        sample_vehicle.mark_as_sold()
        await test_db.save_vehicle(sample_vehicle)
        
        new_vehicles = await test_db.get_vehicles_by_status(VehicleStatus.NEW)
        assert len(new_vehicles) == 0
        
        sold_vehicles = await test_db.get_vehicles_by_status(VehicleStatus.SOLD)
        assert len(sold_vehicles) == 1
    
    @pytest.mark.asyncio
    async def test_get_new_vehicles(self, test_db, sample_vehicle):
        """Test getting new vehicles that haven't been notified."""
        # Save vehicle (new and not notified)
        await test_db.save_vehicle(sample_vehicle)
        
        new_vehicles = await test_db.get_new_vehicles()
        assert len(new_vehicles) == 1
        
        # Mark as notified
        sample_vehicle.mark_notification_sent()
        await test_db.save_vehicle(sample_vehicle)
        
        new_vehicles = await test_db.get_new_vehicles()
        assert len(new_vehicles) == 0
    
    @pytest.mark.asyncio
    async def test_mark_vehicles_as_sold(self, test_db, sample_vehicle):
        """Test marking vehicles as sold."""
        # Save vehicle
        await test_db.save_vehicle(sample_vehicle)
        
        # Mark as sold (not in current inventory)
        count = await test_db.mark_vehicles_as_sold([])
        assert count == 1
        
        # Verify status changed
        retrieved = await test_db.get_vehicle(sample_vehicle.vin)
        assert retrieved.status == VehicleStatus.SOLD
    
    @pytest.mark.asyncio
    async def test_cleanup_old_vehicles(self, test_db, sample_vehicle):
        """Test cleaning up old vehicles."""
        # Save vehicle and mark as sold
        sample_vehicle.mark_as_sold()
        # Set last_seen to old date
        sample_vehicle.last_seen = datetime.now() - timedelta(days=35)
        await test_db.save_vehicle(sample_vehicle)
        
        # Cleanup vehicles older than 30 days
        count = await test_db.cleanup_old_vehicles(30)
        assert count == 1
        
        # Vehicle should be gone
        retrieved = await test_db.get_vehicle(sample_vehicle.vin)
        assert retrieved is None
    
    @pytest.mark.asyncio
    async def test_get_statistics(self, test_db, sample_vehicle):
        """Test getting database statistics."""
        # Initial stats
        stats = await test_db.get_statistics()
        assert stats['total_vehicles'] == 0
        
        # Add vehicle
        await test_db.save_vehicle(sample_vehicle)
        
        stats = await test_db.get_statistics()
        assert stats['total_vehicles'] == 1
        assert stats['by_status']['new'] == 1
        assert stats['price_stats']['min'] == sample_vehicle.price
        assert stats['price_stats']['max'] == sample_vehicle.price
        assert stats['seen_last_24h'] == 1
    
    @pytest.mark.asyncio
    async def test_export_to_csv(self, test_db, sample_vehicle, temp_dir):
        """Test exporting vehicles to CSV."""
        # Save vehicle
        await test_db.save_vehicle(sample_vehicle)
        
        # Export to CSV
        csv_path = temp_dir / "export.csv"
        count = await test_db.export_to_csv(csv_path)
        
        assert count == 1
        assert csv_path.exists()
        
        # Check CSV content
        content = csv_path.read_text(encoding='utf-8')
        assert sample_vehicle.vin in content
        assert str(sample_vehicle.price) in content
