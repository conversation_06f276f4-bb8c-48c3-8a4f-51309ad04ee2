"""Configuration settings for Tesla Inventory Monitor."""

import json
import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, <PERSON>, validator
from pydantic_settings import BaseSettings


class ApiConfig(BaseModel):
    """Tesla API configuration with enhanced connectivity settings."""
    base_url: str = "https://www.tesla.com"
    inventory_endpoint: str = "/inventory/api/v1/inventory-results"
    timeout_seconds: int = 45  # Increased timeout
    max_retries: int = 5  # More retries
    retry_delay_seconds: int = 3  # Longer delay between retries
    rate_limit_delay: float = 2.0  # Longer rate limiting
    user_agent: str = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    )
    # New settings for enhanced connectivity
    enable_http2: bool = False  # Disable HTTP/2 by default due to connection issues
    enable_user_agent_rotation: bool = True
    enable_endpoint_fallback: bool = True
    human_like_delays: bool = True
    # Proxy support for regions where Tesla is blocked
    proxy_url: Optional[str] = None  # e.g., "http://proxy:8080" or "socks5://proxy:1080"


class FilterConfig(BaseModel):
    """Vehicle filtering configuration."""
    model: str = "my"  # Model Y
    market: str = "TR"  # Turkey
    condition: List[str] = ["new", "used"]
    price_min: Optional[int] = None
    price_max: Optional[int] = 2000000  # 2M TL default max
    year_min: Optional[int] = 2022
    year_max: Optional[int] = None
    trim_levels: List[str] = []
    colors: List[str] = []
    
    @validator('condition')
    def validate_condition(cls, v):
        valid_conditions = ['new', 'used']
        for condition in v:
            if condition.lower() not in valid_conditions:
                raise ValueError(f"Invalid condition: {condition}. Must be one of {valid_conditions}")
        return [c.lower() for c in v]


class MonitoringConfig(BaseModel):
    """Monitoring behavior configuration."""
    check_interval_minutes: int = 5
    enable_notifications: bool = True
    notification_sound: bool = True
    max_concurrent_requests: int = 3
    run_once: bool = False


class DesktopNotificationConfig(BaseModel):
    """Desktop notification configuration."""
    enabled: bool = True
    title: str = "Tesla Monitor"
    timeout: int = 10


class EmailNotificationConfig(BaseModel):
    """Email notification configuration."""
    enabled: bool = False
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    username: str = ""
    password: str = ""
    to_email: str = ""
    use_tls: bool = True


class NotificationConfig(BaseModel):
    """Notification configuration."""
    desktop: DesktopNotificationConfig = DesktopNotificationConfig()
    email: EmailNotificationConfig = EmailNotificationConfig()


class DatabaseConfig(BaseModel):
    """Database configuration."""
    path: str = "vehicles.db"
    cleanup_days: int = 30
    
    @property
    def full_path(self) -> Path:
        """Get full database path."""
        return Path(self.path).resolve()


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    file: str = "tesla_monitor.log"
    max_size_mb: int = 10
    backup_count: int = 5
    console_output: bool = True
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}. Must be one of {valid_levels}")
        return v.upper()


class Settings(BaseSettings):
    """Main application settings."""
    
    api: ApiConfig = ApiConfig()
    filters: FilterConfig = FilterConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    notifications: NotificationConfig = NotificationConfig()
    database: DatabaseConfig = DatabaseConfig()
    logging: LoggingConfig = LoggingConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
    
    @classmethod
    def load_from_file(cls, config_path: Path) -> "Settings":
        """Load settings from JSON configuration file."""
        if not config_path.exists():
            # Create default config file
            default_settings = cls()
            default_settings.save_to_file(config_path)
            return default_settings
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"Invalid configuration file {config_path}: {e}")
    
    def save_to_file(self, config_path: Path) -> None:
        """Save settings to JSON configuration file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(
                self.dict(),
                f,
                indent=2,
                ensure_ascii=False
            )
    
    def dict(self, **kwargs) -> Dict[str, Any]:
        """Convert to dictionary with proper serialization."""
        return super().dict(exclude_none=False, **kwargs)
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update settings from dictionary."""
        for key, value in data.items():
            if hasattr(self, key):
                if isinstance(getattr(self, key), BaseModel):
                    # Update nested model
                    current_value = getattr(self, key)
                    if isinstance(value, dict):
                        for nested_key, nested_value in value.items():
                            if hasattr(current_value, nested_key):
                                setattr(current_value, nested_key, nested_value)
                else:
                    setattr(self, key, value)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings(config_path: Optional[Path] = None) -> Settings:
    """Get global settings instance."""
    global _settings
    
    if _settings is None:
        if config_path is None:
            config_path = Path("config.json")
        _settings = Settings.load_from_file(config_path)
    
    return _settings


def reload_settings(config_path: Optional[Path] = None) -> Settings:
    """Reload settings from file."""
    global _settings
    _settings = None
    return get_settings(config_path)


def get_default_config_path() -> Path:
    """Get default configuration file path."""
    return Path("config.json")


def create_default_config(config_path: Optional[Path] = None) -> Path:
    """Create default configuration file."""
    if config_path is None:
        config_path = get_default_config_path()
    
    settings = Settings()
    settings.save_to_file(config_path)
    return config_path
