"""Database management for Tesla Inventory Monitor."""

import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any
import aiosqlite
from loguru import logger

from .vehicle import Vehicle, VehicleStatus


class DatabaseManager:
    """Async SQLite database manager for vehicle tracking."""
    
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self._ensure_db_directory()
    
    def _ensure_db_directory(self) -> None:
        """Ensure database directory exists."""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize database with required tables."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS vehicles (
                    vin TEXT PRIMARY KEY,
                    model TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    trim TEXT,
                    condition TEXT NOT NULL,
                    price INTEGER NOT NULL,
                    currency TEXT DEFAULT 'TRY',
                    exterior_color TEXT,
                    interior_color TEXT,
                    wheels TEXT,
                    autopilot TEXT,
                    location TEXT,
                    delivery_estimate TEXT,
                    is_demo BOOLEAN DEFAULT FALSE,
                    first_seen TIMESTAMP NOT NULL,
                    last_seen TIMESTAMP NOT NULL,
                    status TEXT NOT NULL DEFAULT 'new',
                    notification_sent BOOLEAN DEFAULT FALSE,
                    raw_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better performance
            await db.execute("CREATE INDEX IF NOT EXISTS idx_vehicles_status ON vehicles(status)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_vehicles_model ON vehicles(model)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_vehicles_price ON vehicles(price)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_vehicles_year ON vehicles(year)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_vehicles_last_seen ON vehicles(last_seen)")
            
            await db.commit()
            logger.info(f"Database initialized at {self.db_path}")
    
    async def save_vehicle(self, vehicle: Vehicle) -> None:
        """Save or update vehicle in database."""
        async with aiosqlite.connect(self.db_path) as db:
            # Check if vehicle exists
            cursor = await db.execute("SELECT vin FROM vehicles WHERE vin = ?", (vehicle.vin,))
            exists = await cursor.fetchone()
            
            if exists:
                # Update existing vehicle
                await db.execute("""
                    UPDATE vehicles SET
                        model = ?, year = ?, trim = ?, condition = ?, price = ?, currency = ?,
                        exterior_color = ?, interior_color = ?, wheels = ?, autopilot = ?,
                        location = ?, delivery_estimate = ?, is_demo = ?,
                        last_seen = ?, status = ?, notification_sent = ?, raw_data = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE vin = ?
                """, (
                    vehicle.model, vehicle.year, vehicle.trim, vehicle.condition.value, 
                    vehicle.price, vehicle.currency, vehicle.exterior_color, vehicle.interior_color,
                    vehicle.wheels, vehicle.autopilot, vehicle.location, vehicle.delivery_estimate,
                    vehicle.is_demo, vehicle.last_seen.isoformat(), vehicle.status.value,
                    vehicle.notification_sent, json.dumps(vehicle.raw_data), vehicle.vin
                ))
                logger.debug(f"Updated vehicle {vehicle.vin} in database")
            else:
                # Insert new vehicle
                await db.execute("""
                    INSERT INTO vehicles (
                        vin, model, year, trim, condition, price, currency,
                        exterior_color, interior_color, wheels, autopilot,
                        location, delivery_estimate, is_demo,
                        first_seen, last_seen, status, notification_sent, raw_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    vehicle.vin, vehicle.model, vehicle.year, vehicle.trim, vehicle.condition.value,
                    vehicle.price, vehicle.currency, vehicle.exterior_color, vehicle.interior_color,
                    vehicle.wheels, vehicle.autopilot, vehicle.location, vehicle.delivery_estimate,
                    vehicle.is_demo, vehicle.first_seen.isoformat(), vehicle.last_seen.isoformat(),
                    vehicle.status.value, vehicle.notification_sent, json.dumps(vehicle.raw_data)
                ))
                logger.debug(f"Inserted new vehicle {vehicle.vin} into database")
            
            await db.commit()
    
    async def get_vehicle(self, vin: str) -> Optional[Vehicle]:
        """Get vehicle by VIN."""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute("SELECT * FROM vehicles WHERE vin = ?", (vin,))
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_vehicle(row)
            return None
    
    async def get_all_vehicles(self, limit: Optional[int] = None) -> List[Vehicle]:
        """Get all vehicles from database."""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            query = "SELECT * FROM vehicles ORDER BY last_seen DESC"
            if limit:
                query += f" LIMIT {limit}"
            
            cursor = await db.execute(query)
            rows = await cursor.fetchall()
            
            return [self._row_to_vehicle(row) for row in rows]
    
    async def get_vehicles_by_status(self, status: VehicleStatus) -> List[Vehicle]:
        """Get vehicles by status."""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute(
                "SELECT * FROM vehicles WHERE status = ? ORDER BY last_seen DESC",
                (status.value,)
            )
            rows = await cursor.fetchall()
            
            return [self._row_to_vehicle(row) for row in rows]
    
    async def get_new_vehicles(self) -> List[Vehicle]:
        """Get vehicles that are new and haven't been notified."""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            cursor = await db.execute(
                "SELECT * FROM vehicles WHERE status = 'new' AND notification_sent = FALSE ORDER BY first_seen DESC"
            )
            rows = await cursor.fetchall()
            
            return [self._row_to_vehicle(row) for row in rows]
    
    async def mark_vehicles_as_sold(self, current_vins: List[str]) -> int:
        """Mark vehicles as sold if they're not in current inventory."""
        async with aiosqlite.connect(self.db_path) as db:
            # Get all vehicles that are not sold and not in current inventory
            placeholders = ','.join('?' * len(current_vins)) if current_vins else "''"
            query = f"""
                UPDATE vehicles 
                SET status = 'sold', updated_at = CURRENT_TIMESTAMP
                WHERE status != 'sold' AND vin NOT IN ({placeholders})
            """
            
            cursor = await db.execute(query, current_vins)
            count = cursor.rowcount
            await db.commit()
            
            if count > 0:
                logger.info(f"Marked {count} vehicles as sold")
            
            return count
    
    async def cleanup_old_vehicles(self, days: int = 30) -> int:
        """Remove vehicles older than specified days."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "DELETE FROM vehicles WHERE last_seen < ? AND status = 'sold'",
                (cutoff_date.isoformat(),)
            )
            count = cursor.rowcount
            await db.commit()
            
            if count > 0:
                logger.info(f"Cleaned up {count} old vehicles from database")
            
            return count
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        async with aiosqlite.connect(self.db_path) as db:
            stats = {}
            
            # Total vehicles
            cursor = await db.execute("SELECT COUNT(*) FROM vehicles")
            stats['total_vehicles'] = (await cursor.fetchone())[0]
            
            # Vehicles by status
            cursor = await db.execute("""
                SELECT status, COUNT(*) as count 
                FROM vehicles 
                GROUP BY status
            """)
            status_counts = await cursor.fetchall()
            stats['by_status'] = {row[0]: row[1] for row in status_counts}
            
            # Price statistics
            cursor = await db.execute("""
                SELECT 
                    MIN(price) as min_price,
                    MAX(price) as max_price,
                    AVG(price) as avg_price
                FROM vehicles 
                WHERE status != 'sold'
            """)
            price_stats = await cursor.fetchone()
            if price_stats and price_stats[0] is not None:
                stats['price_stats'] = {
                    'min': int(price_stats[0]),
                    'max': int(price_stats[1]),
                    'avg': int(price_stats[2])
                }
            
            # Recent activity
            cursor = await db.execute("""
                SELECT COUNT(*) 
                FROM vehicles 
                WHERE last_seen > datetime('now', '-24 hours')
            """)
            stats['seen_last_24h'] = (await cursor.fetchone())[0]
            
            return stats
    
    def _row_to_vehicle(self, row: aiosqlite.Row) -> Vehicle:
        """Convert database row to Vehicle object."""
        raw_data = {}
        if row['raw_data']:
            try:
                raw_data = json.loads(row['raw_data'])
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse raw_data for vehicle {row['vin']}")
        
        return Vehicle(
            vin=row['vin'],
            model=row['model'],
            year=row['year'],
            trim=row['trim'],
            condition=row['condition'],
            price=row['price'],
            currency=row['currency'] or 'TRY',
            exterior_color=row['exterior_color'],
            interior_color=row['interior_color'],
            wheels=row['wheels'],
            autopilot=row['autopilot'],
            location=row['location'],
            delivery_estimate=row['delivery_estimate'],
            is_demo=bool(row['is_demo']),
            first_seen=datetime.fromisoformat(row['first_seen']),
            last_seen=datetime.fromisoformat(row['last_seen']),
            status=VehicleStatus(row['status']),
            notification_sent=bool(row['notification_sent']),
            raw_data=raw_data
        )
    
    async def export_to_csv(self, output_path: Path) -> int:
        """Export vehicles to CSV file."""
        import csv
        
        vehicles = await self.get_all_vehicles()
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'vin', 'model', 'year', 'trim', 'condition', 'price', 'currency',
                'exterior_color', 'interior_color', 'wheels', 'autopilot',
                'location', 'delivery_estimate', 'is_demo',
                'first_seen', 'last_seen', 'status', 'notification_sent'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for vehicle in vehicles:
                row = {
                    'vin': vehicle.vin,
                    'model': vehicle.model,
                    'year': vehicle.year,
                    'trim': vehicle.trim,
                    'condition': vehicle.condition.value,
                    'price': vehicle.price,
                    'currency': vehicle.currency,
                    'exterior_color': vehicle.exterior_color,
                    'interior_color': vehicle.interior_color,
                    'wheels': vehicle.wheels,
                    'autopilot': vehicle.autopilot,
                    'location': vehicle.location,
                    'delivery_estimate': vehicle.delivery_estimate,
                    'is_demo': vehicle.is_demo,
                    'first_seen': vehicle.first_seen.isoformat(),
                    'last_seen': vehicle.last_seen.isoformat(),
                    'status': vehicle.status.value,
                    'notification_sent': vehicle.notification_sent
                }
                writer.writerow(row)
        
        logger.info(f"Exported {len(vehicles)} vehicles to {output_path}")
        return len(vehicles)
