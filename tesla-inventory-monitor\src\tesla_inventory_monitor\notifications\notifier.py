"""Notification management for Tesla Inventory Monitor."""

import asyncio
import smtplib
import sys
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON>art
from typing import List, Optional
from loguru import logger

from ..config.settings import Settings
from ..models.vehicle import Vehicle

# Windows-specific imports
if sys.platform == "win32":
    try:
        from plyer import notification
        PLYER_AVAILABLE = True
    except ImportError:
        PLYER_AVAILABLE = False
        logger.warning("plyer not available - desktop notifications disabled")
else:
    PLYER_AVAILABLE = False


class NotificationManager:
    """Manages notifications for new Tesla vehicles."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    async def notify_new_vehicles(self, vehicles: List[Vehicle]) -> None:
        """Send notifications for new vehicles."""
        if not vehicles:
            return
        
        if not self.settings.monitoring.enable_notifications:
            logger.info("Notifications disabled in settings")
            return
        
        logger.info(f"Sending notifications for {len(vehicles)} new vehicles")
        
        # Send desktop notification
        if self.settings.notifications.desktop.enabled:
            await self._send_desktop_notification(vehicles)
        
        # Send email notification
        if self.settings.notifications.email.enabled:
            await self._send_email_notification(vehicles)
    
    async def _send_desktop_notification(self, vehicles: List[Vehicle]) -> None:
        """Send Windows desktop notification."""
        if not PLYER_AVAILABLE:
            logger.warning("Desktop notifications not available on this system")
            return
        
        try:
            if len(vehicles) == 1:
                vehicle = vehicles[0]
                title = self.settings.notifications.desktop.title
                message = (
                    f"New {vehicle.display_name} found!\n"
                    f"Price: {vehicle.formatted_price}\n"
                    f"VIN: {vehicle.vin}"
                )
                if vehicle.location:
                    message += f"\nLocation: {vehicle.location}"
            else:
                title = f"{self.settings.notifications.desktop.title} - {len(vehicles)} New Vehicles"
                message = f"Found {len(vehicles)} new Tesla vehicles:\n"
                for i, vehicle in enumerate(vehicles[:3]):  # Show first 3
                    message += f"• {vehicle.display_name} - {vehicle.formatted_price}\n"
                if len(vehicles) > 3:
                    message += f"... and {len(vehicles) - 3} more"
            
            # Send notification
            notification.notify(
                title=title,
                message=message,
                timeout=self.settings.notifications.desktop.timeout,
                app_name="Tesla Monitor",
                app_icon=None  # Could add an icon path here
            )
            
            logger.info("Desktop notification sent successfully")
            
        except Exception as e:
            logger.error(f"Failed to send desktop notification: {e}")
    
    async def _send_email_notification(self, vehicles: List[Vehicle]) -> None:
        """Send email notification."""
        if not self.settings.notifications.email.username:
            logger.warning("Email notifications enabled but no username configured")
            return
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"Tesla Monitor - {len(vehicles)} New Vehicle{'s' if len(vehicles) > 1 else ''} Found"
            msg['From'] = self.settings.notifications.email.username
            msg['To'] = self.settings.notifications.email.to_email
            
            # Create text content
            text_content = self._create_email_text(vehicles)
            html_content = self._create_email_html(vehicles)
            
            # Attach parts
            text_part = MIMEText(text_content, 'plain', 'utf-8')
            html_part = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Send email
            await self._send_email(msg)
            
            logger.info(f"Email notification sent to {self.settings.notifications.email.to_email}")
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
    
    def _create_email_text(self, vehicles: List[Vehicle]) -> str:
        """Create plain text email content."""
        content = f"Tesla Monitor found {len(vehicles)} new vehicle{'s' if len(vehicles) > 1 else ''}:\n\n"
        
        for vehicle in vehicles:
            content += f"• {vehicle.display_name}\n"
            content += f"  Price: {vehicle.formatted_price}\n"
            content += f"  Year: {vehicle.year}\n"
            content += f"  Condition: {vehicle.condition.value.title()}\n"
            content += f"  VIN: {vehicle.vin}\n"
            
            if vehicle.exterior_color:
                content += f"  Exterior: {vehicle.exterior_color}\n"
            if vehicle.interior_color:
                content += f"  Interior: {vehicle.interior_color}\n"
            if vehicle.location:
                content += f"  Location: {vehicle.location}\n"
            if vehicle.delivery_estimate:
                content += f"  Delivery: {vehicle.delivery_estimate}\n"
            
            content += "\n"
        
        content += "Happy Tesla hunting!\n"
        content += "Tesla Inventory Monitor"
        
        return content
    
    def _create_email_html(self, vehicles: List[Vehicle]) -> str:
        """Create HTML email content."""
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ color: #e31e24; font-size: 24px; font-weight: bold; margin-bottom: 20px; }}
                .vehicle {{ border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; }}
                .vehicle-title {{ font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px; }}
                .price {{ font-size: 20px; font-weight: bold; color: #e31e24; }}
                .detail {{ margin: 5px 0; }}
                .label {{ font-weight: bold; }}
                .footer {{ margin-top: 30px; color: #666; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="header">🚗 Tesla Monitor Alert</div>
            <p>Found {len(vehicles)} new Tesla vehicle{'s' if len(vehicles) > 1 else ''}:</p>
        """
        
        for vehicle in vehicles:
            html += f"""
            <div class="vehicle">
                <div class="vehicle-title">{vehicle.display_name}</div>
                <div class="price">{vehicle.formatted_price}</div>
                <div class="detail"><span class="label">Year:</span> {vehicle.year}</div>
                <div class="detail"><span class="label">Condition:</span> {vehicle.condition.value.title()}</div>
                <div class="detail"><span class="label">VIN:</span> {vehicle.vin}</div>
            """
            
            if vehicle.exterior_color:
                html += f'<div class="detail"><span class="label">Exterior:</span> {vehicle.exterior_color}</div>'
            if vehicle.interior_color:
                html += f'<div class="detail"><span class="label">Interior:</span> {vehicle.interior_color}</div>'
            if vehicle.location:
                html += f'<div class="detail"><span class="label">Location:</span> {vehicle.location}</div>'
            if vehicle.delivery_estimate:
                html += f'<div class="detail"><span class="label">Delivery:</span> {vehicle.delivery_estimate}</div>'
            
            html += "</div>"
        
        html += """
            <div class="footer">
                <p>Happy Tesla hunting!</p>
                <p><strong>Tesla Inventory Monitor</strong></p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    async def _send_email(self, msg: MIMEMultipart) -> None:
        """Send email using SMTP."""
        # Run in thread pool to avoid blocking
        await asyncio.get_event_loop().run_in_executor(
            None, self._send_email_sync, msg
        )
    
    def _send_email_sync(self, msg: MIMEMultipart) -> None:
        """Send email synchronously."""
        server = None
        try:
            # Create SMTP connection
            server = smtplib.SMTP(
                self.settings.notifications.email.smtp_server,
                self.settings.notifications.email.smtp_port
            )
            
            if self.settings.notifications.email.use_tls:
                server.starttls()
            
            # Login
            server.login(
                self.settings.notifications.email.username,
                self.settings.notifications.email.password
            )
            
            # Send message
            server.send_message(msg)
            
        finally:
            if server:
                server.quit()
    
    async def test_desktop_notification(self) -> bool:
        """Test desktop notification."""
        if not PLYER_AVAILABLE:
            logger.error("Desktop notifications not available")
            return False
        
        try:
            notification.notify(
                title="Tesla Monitor Test",
                message="Desktop notifications are working!",
                timeout=5,
                app_name="Tesla Monitor"
            )
            logger.info("Test desktop notification sent")
            return True
        except Exception as e:
            logger.error(f"Desktop notification test failed: {e}")
            return False
    
    async def test_email_notification(self) -> bool:
        """Test email notification."""
        if not self.settings.notifications.email.enabled:
            logger.error("Email notifications not enabled")
            return False
        
        if not self.settings.notifications.email.username:
            logger.error("Email username not configured")
            return False
        
        try:
            # Create test message
            msg = MIMEMultipart()
            msg['Subject'] = "Tesla Monitor - Test Email"
            msg['From'] = self.settings.notifications.email.username
            msg['To'] = self.settings.notifications.email.to_email
            
            text_content = "This is a test email from Tesla Monitor. Email notifications are working!"
            msg.attach(MIMEText(text_content, 'plain', 'utf-8'))
            
            await self._send_email(msg)
            logger.info("Test email sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Email notification test failed: {e}")
            return False
