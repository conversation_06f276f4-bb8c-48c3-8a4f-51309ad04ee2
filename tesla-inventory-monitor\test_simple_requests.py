#!/usr/bin/env python3
"""
Simple test using requests library to isolate the issue.
"""

import requests
import json
import time

def test_with_requests():
    """Test Tesla access with requests library."""
    print("🔍 Testing with requests library...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    # Test basic connectivity
    urls_to_test = [
        "https://www.tesla.com/tr_tr/",
        "https://www.tesla.com/",
        "https://tesla.com/",
        "https://www.google.com/"  # Control test
    ]
    
    for url in urls_to_test:
        print(f"\n  Testing: {url}")
        try:
            response = requests.get(url, headers=headers, timeout=30)
            print(f"    ✅ Status: {response.status_code}")
            print(f"    Response size: {len(response.content)} bytes")
            if response.status_code == 200:
                print(f"    ✅ SUCCESS!")
                if "tesla" in url.lower():
                    return True
        except requests.exceptions.Timeout:
            print(f"    ❌ Timeout")
        except requests.exceptions.ConnectionError as e:
            print(f"    ❌ Connection Error: {str(e)[:100]}")
        except Exception as e:
            print(f"    ❌ Error: {str(e)[:100]}")
    
    return False

def test_tesla_api_with_requests():
    """Test Tesla API with requests."""
    print("\n🔍 Testing Tesla API with requests...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
        'Referer': 'https://www.tesla.com/tr_tr/inventory/new/my',
        'Origin': 'https://www.tesla.com',
    }
    
    # Turkish inventory API parameters
    query_obj = {
        'model': 'my',
        'condition': 'new',
        'options': {},
        'arrangeby': 'Price',
        'order': 'asc',
        'market': 'TR',
        'language': 'tr',
        'super_region': 'europe',
        'lng': 28.9784,
        'lat': 41.0082,
        'zip': '34000',
        'range': 200
    }
    
    params = {
        'query': json.dumps(query_obj),
        'offset': 0,
        'count': 10,
        'outsideOffset': 0,
        'outsideSearch': False
    }
    
    endpoints = [
        "https://www.tesla.com/tr_tr/inventory/api/v1/inventory-results",
        "https://www.tesla.com/inventory/api/v1/inventory-results",
    ]
    
    for endpoint in endpoints:
        print(f"\n  Testing API: {endpoint}")
        try:
            response = requests.get(endpoint, headers=headers, params=params, timeout=45)
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    results = data.get('results', [])
                    print(f"    ✅ SUCCESS! Found {len(results)} vehicles")
                    return True, endpoint
                except json.JSONDecodeError:
                    print(f"    ⚠️  Status 200 but invalid JSON")
                    print(f"    Response: {response.text[:200]}...")
            else:
                print(f"    Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"    ❌ Timeout")
        except requests.exceptions.ConnectionError as e:
            print(f"    ❌ Connection Error: {str(e)[:100]}")
        except Exception as e:
            print(f"    ❌ Error: {str(e)[:100]}")
    
    return False, None

def main():
    """Main test function."""
    print("🚗 Simple Requests Test")
    print("=" * 40)
    
    start_time = time.time()
    
    # Test basic connectivity
    basic_ok = test_with_requests()
    
    if basic_ok:
        print("\n✅ Basic connectivity working with requests library")
        
        # Test API
        api_success, working_endpoint = test_tesla_api_with_requests()
        if api_success:
            print(f"\n🎉 SUCCESS! Working endpoint: {working_endpoint}")
        else:
            print(f"\n❌ API endpoints failed")
    else:
        print("\n❌ Basic connectivity failed even with requests library")
        print("This suggests a network-level connectivity issue.")
        print("\nPossible causes:")
        print("- Internet connection issues")
        print("- Firewall blocking HTTPS traffic")
        print("- DNS resolution problems")
        print("- ISP blocking Tesla domains")
        print("\nTroubleshooting steps:")
        print("1. Try accessing https://www.tesla.com/tr_tr/ in your browser")
        print("2. Check if you can access other HTTPS sites")
        print("3. Try using a VPN or mobile hotspot")
        print("4. Check Windows Firewall settings")
    
    elapsed = time.time() - start_time
    print(f"\nTest completed in {elapsed:.1f} seconds")

if __name__ == "__main__":
    main()
