2025-06-28 11:46:35 | INFO     | tesla_inventory_monitor.utils.logging:setup_logging:52 - Logging configured - Level: INFO, File: tesla_monitor.log
2025-06-28 11:46:35 | INFO     | tesla_inventory_monitor.models.database:initialize:62 - Database initialized at C:\Users\<USER>\OneDrive\Masaüstü\Tesla Monitor\tesla-inventory-monitor\vehicles.db
2025-06-28 11:46:35 | INFO     | tesla_inventory_monitor.core.monitor:initialize:27 - Inventory monitor initialized
2025-06-28 11:46:35 | INFO     | tesla_inventory_monitor.core.client:test_connection:234 - Testing Tesla API connection...
2025-06-28 11:47:05 | WARNING  | tesla_inventory_monitor.core.client:_make_request:100 - Request failed (attempt 1/3): Timeout after 30s: 
2025-06-28 11:47:05 | INFO     | tesla_inventory_monitor.core.client:_make_request:125 - Retrying in 2 seconds...
2025-06-28 11:47:38 | WARNING  | tesla_inventory_monitor.core.client:_make_request:100 - Request failed (attempt 2/3): Timeout after 30s: 
2025-06-28 11:47:38 | INFO     | tesla_inventory_monitor.core.client:_make_request:125 - Retrying in 4 seconds...
2025-06-28 11:48:09 | WARNING  | tesla_inventory_monitor.core.client:_make_request:115 - Request failed (attempt 3/3): Request error:  (Type: ReadError)
2025-06-28 11:48:09 | ERROR    | tesla_inventory_monitor.core.client:_make_request:130 - Request failed after 3 attempts. Last error: Request error:  (Type: ReadError)
2025-06-28 11:48:09 | ERROR    | tesla_inventory_monitor.core.client:test_connection:239 - Tesla API connection test failed: Request failed after 3 attempts. Last error: Request error:  (Type: ReadError)
