"""Logging configuration for Tesla Inventory Monitor."""

import sys
from pathlib import Path
from loguru import logger

from ..config.settings import Settings


def setup_logging(settings: Settings) -> None:
    """Configure logging based on settings."""
    
    # Remove default logger
    logger.remove()
    
    # Console logging
    if settings.logging.console_output:
        logger.add(
            sys.stderr,
            level=settings.logging.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
    
    # File logging
    if settings.logging.file:
        log_path = Path(settings.logging.file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_path,
            level=settings.logging.level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=f"{settings.logging.max_size_mb} MB",
            retention=settings.logging.backup_count,
            compression="zip",
            encoding="utf-8"
        )
    
    # Set log level for specific modules
    logger.level("DEBUG" if settings.logging.level == "DEBUG" else "INFO")
    
    # Suppress noisy third-party loggers
    import logging
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    logger.info(f"Logging configured - Level: {settings.logging.level}, File: {settings.logging.file}")


def get_logger(name: str):
    """Get a logger instance for a specific module."""
    return logger.bind(name=name)
