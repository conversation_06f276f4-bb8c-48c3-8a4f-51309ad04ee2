[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tesla-inventory-monitor"
version = "1.0.0"
description = "Modern Tesla inventory monitoring application for Turkey market"
authors = [
    {name = "Tesla Monitor", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["tesla", "inventory", "monitoring", "turkey"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "httpx>=0.25.2",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "rich>=13.7.0",
    "typer>=0.9.0",
    "click>=8.1.7",
    "sqlalchemy>=2.0.23",
    "aiosqlite>=0.19.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.2",
    "schedule>=1.2.0",
    "plyer>=2.1.0",
    "pywin32>=306; sys_platform == 'win32'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
]

[project.scripts]
tesla-monitor = "tesla_inventory_monitor.cli:main"

[project.urls]
Homepage = "https://github.com/example/tesla-inventory-monitor"
Repository = "https://github.com/example/tesla-inventory-monitor"
Issues = "https://github.com/example/tesla-inventory-monitor/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
]
asyncio_mode = "auto"
