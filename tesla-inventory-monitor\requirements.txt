# Tesla Inventory Monitor - Dependencies
# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
httpx[http2]==0.25.2
pydantic==2.5.0
pydantic-settings==2.1.0

# CLI and UI
rich==13.7.0
typer==0.9.0
click==8.1.7

# Database
sqlalchemy==2.0.23
aiosqlite==0.19.0

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
schedule==1.2.0

# Notifications
plyer==2.1.0  # For Windows notifications
# smtplib is built-in to Python - no additional package needed

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Windows specific
pywin32==306; sys_platform == "win32"
