"""Vehicle data models."""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class VehicleStatus(str, Enum):
    """Vehicle status enumeration."""
    NEW = "new"
    SEEN = "seen"
    SOLD = "sold"
    UNAVAILABLE = "unavailable"


class VehicleCondition(str, Enum):
    """Vehicle condition enumeration."""
    NEW = "new"
    USED = "used"
    DEMO = "demo"


class Vehicle(BaseModel):
    """Tesla vehicle model."""
    
    # Tesla API fields
    vin: str = Field(..., description="Vehicle Identification Number")
    model: str = Field(..., description="Vehicle model (e.g., 'my' for Model Y)")
    year: int = Field(..., description="Vehicle year")
    trim: Optional[str] = Field(None, description="Trim level")
    condition: VehicleCondition = Field(..., description="Vehicle condition")
    price: int = Field(..., description="Price in Turkish Lira")
    currency: str = Field(default="TRY", description="Currency code")
    
    # Vehicle details
    exterior_color: Optional[str] = Field(None, description="Exterior color")
    interior_color: Optional[str] = Field(None, description="Interior color")
    wheels: Optional[str] = Field(None, description="Wheel type")
    autopilot: Optional[str] = Field(None, description="Autopilot package")
    
    # Location and availability
    location: Optional[str] = Field(None, description="Vehicle location")
    delivery_estimate: Optional[str] = Field(None, description="Estimated delivery")
    is_demo: bool = Field(default=False, description="Is demo vehicle")
    
    # Tracking fields
    first_seen: datetime = Field(default_factory=datetime.now, description="First time seen")
    last_seen: datetime = Field(default_factory=datetime.now, description="Last time seen")
    status: VehicleStatus = Field(default=VehicleStatus.NEW, description="Vehicle status")
    notification_sent: bool = Field(default=False, description="Whether notification was sent")
    
    # Raw data
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="Raw API response")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    @validator('vin')
    def validate_vin(cls, v):
        """Validate VIN format."""
        if not v or len(v) != 17:
            raise ValueError("VIN must be 17 characters long")
        return v.upper()
    
    @validator('price')
    def validate_price(cls, v):
        """Validate price is positive."""
        if v <= 0:
            raise ValueError("Price must be positive")
        return v
    
    @validator('year')
    def validate_year(cls, v):
        """Validate year is reasonable."""
        current_year = datetime.now().year
        if v < 2012 or v > current_year + 2:
            raise ValueError(f"Year must be between 2012 and {current_year + 2}")
        return v
    
    @classmethod
    def from_tesla_api(cls, data: Dict[str, Any]) -> "Vehicle":
        """Create Vehicle from Tesla API response."""
        # Extract basic information
        vin = data.get('VIN', '')
        model = data.get('Model', '').lower()
        year = data.get('Year', 0)
        price = data.get('Price', 0)
        
        # Extract condition
        condition_str = data.get('condition', 'new').lower()
        try:
            condition = VehicleCondition(condition_str)
        except ValueError:
            condition = VehicleCondition.NEW
        
        # Extract colors and options
        paint_data = data.get('PAINT', [])
        exterior_color = None
        if isinstance(paint_data, list) and paint_data:
            exterior_color = paint_data[0].get('value', '')
        
        interior_data = data.get('INTERIOR', [])
        interior_color = None
        if isinstance(interior_data, list) and interior_data:
            interior_color = interior_data[0].get('value', '')
        
        # Extract other details
        trim = data.get('TrimName', '')
        wheels = data.get('WHEELS', {}).get('value', '') if isinstance(data.get('WHEELS'), dict) else ''
        autopilot = data.get('AUTOPILOT', {}).get('value', '') if isinstance(data.get('AUTOPILOT'), dict) else ''
        
        # Location and delivery
        location = data.get('City', '') or data.get('Location', '')
        delivery_estimate = data.get('EstimatedDelivery', '')
        is_demo = data.get('IsDemo', False) or 'demo' in condition_str
        
        return cls(
            vin=vin,
            model=model,
            year=year,
            trim=trim,
            condition=condition,
            price=price,
            exterior_color=exterior_color,
            interior_color=interior_color,
            wheels=wheels,
            autopilot=autopilot,
            location=location,
            delivery_estimate=delivery_estimate,
            is_demo=is_demo,
            raw_data=data
        )
    
    def update_last_seen(self) -> None:
        """Update last seen timestamp."""
        self.last_seen = datetime.now()
        if self.status == VehicleStatus.NEW:
            self.status = VehicleStatus.SEEN
    
    def mark_as_sold(self) -> None:
        """Mark vehicle as sold."""
        self.status = VehicleStatus.SOLD
    
    def mark_notification_sent(self) -> None:
        """Mark that notification was sent for this vehicle."""
        self.notification_sent = True
    
    @property
    def display_name(self) -> str:
        """Get display name for the vehicle."""
        parts = [f"{self.year} Tesla"]
        
        if self.model.upper() == "MY":
            parts.append("Model Y")
        elif self.model.upper() == "M3":
            parts.append("Model 3")
        elif self.model.upper() == "MS":
            parts.append("Model S")
        elif self.model.upper() == "MX":
            parts.append("Model X")
        else:
            parts.append(f"Model {self.model.upper()}")
        
        if self.trim:
            parts.append(self.trim)
        
        return " ".join(parts)
    
    @property
    def formatted_price(self) -> str:
        """Get formatted price string."""
        return f"{self.price:,} {self.currency}"
    
    @property
    def is_new_vehicle(self) -> bool:
        """Check if this is a new vehicle that hasn't been notified."""
        return self.status == VehicleStatus.NEW and not self.notification_sent


class VehicleFilter(BaseModel):
    """Vehicle filtering criteria."""
    
    model: Optional[str] = None
    condition: Optional[List[VehicleCondition]] = None
    price_min: Optional[int] = None
    price_max: Optional[int] = None
    year_min: Optional[int] = None
    year_max: Optional[int] = None
    trim_levels: Optional[List[str]] = None
    colors: Optional[List[str]] = None
    location: Optional[str] = None
    
    def matches(self, vehicle: Vehicle) -> bool:
        """Check if vehicle matches filter criteria."""
        
        # Model filter
        if self.model and vehicle.model.lower() != self.model.lower():
            return False
        
        # Condition filter
        if self.condition and vehicle.condition not in self.condition:
            return False
        
        # Price filters
        if self.price_min and vehicle.price < self.price_min:
            return False
        if self.price_max and vehicle.price > self.price_max:
            return False
        
        # Year filters
        if self.year_min and vehicle.year < self.year_min:
            return False
        if self.year_max and vehicle.year > self.year_max:
            return False
        
        # Trim filter
        if self.trim_levels and vehicle.trim not in self.trim_levels:
            return False
        
        # Color filter
        if self.colors:
            vehicle_colors = [vehicle.exterior_color, vehicle.interior_color]
            if not any(color in self.colors for color in vehicle_colors if color):
                return False
        
        # Location filter
        if self.location and self.location.lower() not in (vehicle.location or '').lower():
            return False
        
        return True
    
    @classmethod
    def from_config(cls, filter_config) -> "VehicleFilter":
        """Create filter from configuration."""
        conditions = None
        if filter_config.condition:
            conditions = [VehicleCondition(c) for c in filter_config.condition]
        
        return cls(
            model=filter_config.model,
            condition=conditions,
            price_min=filter_config.price_min,
            price_max=filter_config.price_max,
            year_min=filter_config.year_min,
            year_max=filter_config.year_max,
            trim_levels=filter_config.trim_levels if filter_config.trim_levels else None,
            colors=filter_config.colors if filter_config.colors else None
        )
