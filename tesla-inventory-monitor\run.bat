@echo off
REM Tesla Inventory Monitor - Quick Run Script
REM This script activates the virtual environment and runs Tesla Monitor

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found
    echo Please run install.bat first
    pause
    exit /b 1
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Check if config exists
if not exist "config.json" (
    echo No configuration file found. Creating default configuration...
    tesla-monitor init
    echo.
    echo Please edit config.json to set your preferences, then run this script again.
    pause
    exit /b 0
)

REM Run Tesla Monitor
echo Starting Tesla Inventory Monitor...
tesla-monitor start

pause
