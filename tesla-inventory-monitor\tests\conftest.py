"""Test configuration and fixtures."""

import asyncio
import tempfile
from pathlib import Path
from typing import Async<PERSON>enerator, Generator

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock

from tesla_inventory_monitor.config.settings import Settings
from tesla_inventory_monitor.models.database import DatabaseManager
from tesla_inventory_monitor.models.vehicle import Vehicle, VehicleCondition, VehicleStatus


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_settings(temp_dir: Path) -> Settings:
    """Create test settings with temporary database."""
    settings = Settings()
    settings.database.path = str(temp_dir / "test.db")
    settings.logging.file = str(temp_dir / "test.log")
    settings.logging.level = "DEBUG"
    settings.api.rate_limit_delay = 0  # No delay in tests
    settings.monitoring.check_interval_minutes = 1  # Fast for tests
    return settings


@pytest_asyncio.fixture
async def test_db(test_settings: Settings) -> AsyncGenerator[DatabaseManager, None]:
    """Create a test database."""
    db = DatabaseManager(test_settings.database.full_path)
    await db.initialize()
    yield db


@pytest.fixture
def sample_vehicle() -> Vehicle:
    """Create a sample vehicle for testing."""
    return Vehicle(
        vin="5YJ3E1EA4NF123456",
        model="my",
        year=2023,
        trim="Long Range",
        condition=VehicleCondition.NEW,
        price=1800000,
        currency="TRY",
        exterior_color="Pearl White Multi-Coat",
        interior_color="Black",
        wheels="19\" Gemini Wheels",
        autopilot="Enhanced Autopilot",
        location="Istanbul",
        delivery_estimate="2-4 weeks",
        is_demo=False,
        status=VehicleStatus.NEW,
        notification_sent=False,
        raw_data={"test": "data"}
    )


@pytest.fixture
def sample_tesla_api_response() -> dict:
    """Sample Tesla API response data."""
    return {
        "results": [
            {
                "VIN": "5YJ3E1EA4NF123456",
                "Model": "my",
                "Year": 2023,
                "TrimName": "Long Range",
                "condition": "new",
                "Price": 1800000,
                "PAINT": [{"value": "Pearl White Multi-Coat"}],
                "INTERIOR": [{"value": "Black"}],
                "WHEELS": {"value": "19\" Gemini Wheels"},
                "AUTOPILOT": {"value": "Enhanced Autopilot"},
                "City": "Istanbul",
                "EstimatedDelivery": "2-4 weeks",
                "IsDemo": False
            },
            {
                "VIN": "5YJ3E1EA4NF789012",
                "Model": "my",
                "Year": 2023,
                "TrimName": "Performance",
                "condition": "new",
                "Price": 2200000,
                "PAINT": [{"value": "Solid Black"}],
                "INTERIOR": [{"value": "Black"}],
                "WHEELS": {"value": "21\" Überturbine Wheels"},
                "AUTOPILOT": {"value": "Full Self-Driving Capability"},
                "City": "Ankara",
                "EstimatedDelivery": "1-2 weeks",
                "IsDemo": False
            }
        ]
    }


@pytest.fixture
def mock_httpx_client():
    """Mock httpx client for API testing."""
    mock_client = AsyncMock()
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "results": []
    }
    mock_client.request.return_value = mock_response
    return mock_client


@pytest.fixture
def mock_notification():
    """Mock notification system."""
    with pytest.mock.patch('tesla_inventory_monitor.notifications.notifier.notification') as mock:
        mock.notify = MagicMock()
        yield mock
