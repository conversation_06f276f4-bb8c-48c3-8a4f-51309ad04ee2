"""Tests for vehicle models."""

import pytest
from datetime import datetime

from tesla_inventory_monitor.models.vehicle import Vehicle, VehicleCondition, VehicleStatus, VehicleFilter


class TestVehicle:
    """Test Vehicle model."""
    
    def test_vehicle_creation(self, sample_vehicle):
        """Test basic vehicle creation."""
        assert sample_vehicle.vin == "5YJ3E1EA4NF123456"
        assert sample_vehicle.model == "my"
        assert sample_vehicle.year == 2023
        assert sample_vehicle.condition == VehicleCondition.NEW
        assert sample_vehicle.price == 1800000
    
    def test_vehicle_from_tesla_api(self, sample_tesla_api_response):
        """Test creating vehicle from Tesla API response."""
        api_data = sample_tesla_api_response["results"][0]
        vehicle = Vehicle.from_tesla_api(api_data)
        
        assert vehicle.vin == "5YJ3E1EA4NF123456"
        assert vehicle.model == "my"
        assert vehicle.year == 2023
        assert vehicle.condition == VehicleCondition.NEW
        assert vehicle.price == 1800000
        assert vehicle.exterior_color == "Pearl White Multi-Coat"
        assert vehicle.interior_color == "Black"
    
    def test_vehicle_display_name(self, sample_vehicle):
        """Test vehicle display name generation."""
        expected = "2023 Tesla Model Y Long Range"
        assert sample_vehicle.display_name == expected
    
    def test_vehicle_formatted_price(self, sample_vehicle):
        """Test price formatting."""
        assert sample_vehicle.formatted_price == "1,800,000 TRY"
    
    def test_vehicle_update_last_seen(self, sample_vehicle):
        """Test updating last seen timestamp."""
        original_time = sample_vehicle.last_seen
        sample_vehicle.update_last_seen()
        
        assert sample_vehicle.last_seen > original_time
        assert sample_vehicle.status == VehicleStatus.SEEN
    
    def test_vehicle_mark_as_sold(self, sample_vehicle):
        """Test marking vehicle as sold."""
        sample_vehicle.mark_as_sold()
        assert sample_vehicle.status == VehicleStatus.SOLD
    
    def test_vehicle_mark_notification_sent(self, sample_vehicle):
        """Test marking notification as sent."""
        assert not sample_vehicle.notification_sent
        sample_vehicle.mark_notification_sent()
        assert sample_vehicle.notification_sent
    
    def test_vehicle_is_new_vehicle(self, sample_vehicle):
        """Test new vehicle detection."""
        assert sample_vehicle.is_new_vehicle
        
        sample_vehicle.mark_notification_sent()
        assert not sample_vehicle.is_new_vehicle
        
        sample_vehicle.notification_sent = False
        sample_vehicle.status = VehicleStatus.SEEN
        assert not sample_vehicle.is_new_vehicle
    
    def test_vehicle_validation_invalid_vin(self):
        """Test VIN validation."""
        with pytest.raises(ValueError, match="VIN must be 17 characters"):
            Vehicle(
                vin="INVALID",
                model="my",
                year=2023,
                condition=VehicleCondition.NEW,
                price=1800000
            )
    
    def test_vehicle_validation_invalid_price(self):
        """Test price validation."""
        with pytest.raises(ValueError, match="Price must be positive"):
            Vehicle(
                vin="5YJ3E1EA4NF123456",
                model="my",
                year=2023,
                condition=VehicleCondition.NEW,
                price=-1000
            )
    
    def test_vehicle_validation_invalid_year(self):
        """Test year validation."""
        with pytest.raises(ValueError, match="Year must be between"):
            Vehicle(
                vin="5YJ3E1EA4NF123456",
                model="my",
                year=2050,  # Too far in future
                condition=VehicleCondition.NEW,
                price=1800000
            )


class TestVehicleFilter:
    """Test VehicleFilter model."""
    
    def test_filter_matches_all(self, sample_vehicle):
        """Test filter that matches everything."""
        filter_obj = VehicleFilter()
        assert filter_obj.matches(sample_vehicle)
    
    def test_filter_model(self, sample_vehicle):
        """Test model filtering."""
        filter_obj = VehicleFilter(model="my")
        assert filter_obj.matches(sample_vehicle)
        
        filter_obj = VehicleFilter(model="m3")
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_condition(self, sample_vehicle):
        """Test condition filtering."""
        filter_obj = VehicleFilter(condition=[VehicleCondition.NEW])
        assert filter_obj.matches(sample_vehicle)
        
        filter_obj = VehicleFilter(condition=[VehicleCondition.USED])
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_price_range(self, sample_vehicle):
        """Test price range filtering."""
        # Within range
        filter_obj = VehicleFilter(price_min=1000000, price_max=2000000)
        assert filter_obj.matches(sample_vehicle)
        
        # Below minimum
        filter_obj = VehicleFilter(price_min=2000000)
        assert not filter_obj.matches(sample_vehicle)
        
        # Above maximum
        filter_obj = VehicleFilter(price_max=1000000)
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_year_range(self, sample_vehicle):
        """Test year range filtering."""
        # Within range
        filter_obj = VehicleFilter(year_min=2020, year_max=2025)
        assert filter_obj.matches(sample_vehicle)
        
        # Below minimum
        filter_obj = VehicleFilter(year_min=2025)
        assert not filter_obj.matches(sample_vehicle)
        
        # Above maximum
        filter_obj = VehicleFilter(year_max=2020)
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_trim_levels(self, sample_vehicle):
        """Test trim level filtering."""
        filter_obj = VehicleFilter(trim_levels=["Long Range", "Performance"])
        assert filter_obj.matches(sample_vehicle)
        
        filter_obj = VehicleFilter(trim_levels=["Standard Range"])
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_colors(self, sample_vehicle):
        """Test color filtering."""
        filter_obj = VehicleFilter(colors=["Pearl White Multi-Coat", "Black"])
        assert filter_obj.matches(sample_vehicle)
        
        filter_obj = VehicleFilter(colors=["Red", "Blue"])
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_location(self, sample_vehicle):
        """Test location filtering."""
        filter_obj = VehicleFilter(location="Istanbul")
        assert filter_obj.matches(sample_vehicle)
        
        filter_obj = VehicleFilter(location="Ankara")
        assert not filter_obj.matches(sample_vehicle)
    
    def test_filter_complex(self, sample_vehicle):
        """Test complex filtering with multiple criteria."""
        filter_obj = VehicleFilter(
            model="my",
            condition=[VehicleCondition.NEW],
            price_min=1500000,
            price_max=2000000,
            year_min=2022,
            trim_levels=["Long Range", "Performance"]
        )
        assert filter_obj.matches(sample_vehicle)
        
        # Change one criterion to make it not match
        filter_obj.price_max = 1500000
        assert not filter_obj.matches(sample_vehicle)
